package fm.lizhi.ocean.seal.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.GamePropEventMapping;
import fm.lizhi.ocean.seal.constant.GamePropEventType;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.manager.GameInfoManager;
import fm.lizhi.ocean.seal.manager.LukManger;
import fm.lizhi.ocean.seal.pojo.bo.luk.GrantPropDetail;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.pojo.GamePropGrantResult;
import fm.lizhi.ocean.seal.strategy.GamePropGrantStrategy;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.PublishControlEventRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * LUK 渠道道具发放策略实现
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class LukGamePropGrantStrategy implements GamePropGrantStrategy {

    @Inject
    private LukManger lukManager;

    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameInfoManager gameInfoManager;


    @Override
    public GamePropGrantResult grantProp(GrantGamePropParam param,
                                         GamePropBean gameProp,
                                         GameBizGameBean bizGameBean,
                                         GameInfoBean gameInfoBean,
                                         GameChannelBean gameChannelBean) {
        
        log.info("LUK grant prop, gameId: {}, propId: {}, userId: {}", 
                bizGameBean.getId(), gameProp.getId(), param.getUserId());

        try {
            // 创建 LUK SDK 实例
            SDK lukSdk = lukManager.createLukSdk(bizGameBean, gameChannelBean, param.getAppId());
            if (lukSdk == null) {
                throw new RuntimeException("Failed to create LUK SDK");
            }
            
            // 调用 LUK SDK 发放道具
            Response<?> sdkResult = invokeLukSdk(lukSdk, param, gameProp, gameInfoBean, gameChannelBean);
            
            // 转换结果
            return convertLukResult(sdkResult);
            
        } catch (Exception e) {
            log.error("LUK grant prop failed, gameId: {}, propId: {}, userId: {}", 
                     bizGameBean.getId(), gameProp.getId(), param.getUserId(), e);
            return GamePropGrantResult.failure(-1, "LUK grant prop failed: " + e.getMessage());
        }
    }

    @Override
    public GamePropGrantResult grantProp(GrantGamePropParam param, GamePropBean gameProp) {
        try {
            // 查询gameId是属于游戏渠道的gameId，还是属于seal平台biz_gameId
            GameBizGameBean bizGameBean = bizGameManager.getGame(param.getAppId(), param.getGameId());
            if (bizGameBean == null) {
                throw new IllegalArgumentException("gameId not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
            }

            // 获取游戏信息
            GameInfoBean gameInfoBean = gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
            if (gameInfoBean == null) {
                throw new IllegalArgumentException("gameInfo not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
            }

            // 获取渠道信息
            GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(bizGameBean.getChannelId());
            if (gameChannelBean == null) {
                throw new IllegalArgumentException("channel not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
            }

            // 调用原有的发放方法
            return grantProp(param, gameProp, bizGameBean, gameInfoBean, gameChannelBean);

        } catch (Exception e) {
            log.error("LUK grant prop failed", e);
            return GamePropGrantResult.failure(-1, "LUK grant prop failed: " + e.getMessage());
        }
    }

    @Override
    public boolean supports(String channel) {
        return GameChannel.LUK.equals(channel);
    }

    /**
     * 调用 LUK SDK
     */
    private Response<?> invokeLukSdk(SDK lukSdk,
                                   GrantGamePropParam param,
                                   GamePropBean gameProp,
                                   GameInfoBean gameInfoBean,
                                   GameChannelBean gameChannelBean) throws Exception {
        
        // 获取 LUK 渠道事件
        String channelEvent = GamePropEventMapping.getChannelEvent(GameChannel.LUK, GamePropEventType.GRANT_PROP.getEvent());
        if (channelEvent == null) {
            throw new IllegalArgumentException("No LUK event mapping found for grant prop");
        }
        
        // 构建数据
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("user_id", param.getUserId());
        dataMap.put("details", CollUtil.newArrayList(new GrantPropDetail()
                        .setProp_id(String.valueOf(gameProp.getChannelPropId()))
                        .setNum(param.getNum())
                        .setDuration(param.getDuration())
                        .setDuration_reset(false)
                ));
        dataMap.put("unique_id", param.getUniqueId());

        // 构建 LUK SDK 请求
        PublishControlEventRequest.Builder builder = new PublishControlEventRequest.Builder();
        builder.setAppId(Integer.parseInt(gameChannelBean.getAppId()));
        builder.setGameId(Math.toIntExact(gameInfoBean.getChannelGameId()));
        builder.setTimestamp(System.currentTimeMillis());
        builder.setType(Integer.parseInt(channelEvent));
        builder.setData(JSONObject.toJSONString(dataMap));
        
        return lukSdk.PublishControlEvent(builder.build());
    }

    /**
     * 转换 LUK 结果
     */
    private GamePropGrantResult convertLukResult(Response<?> sdkResult) {
        try {
            if (sdkResult != null) {
                if (sdkResult.suc()) {
                    return GamePropGrantResult.success(JsonUtil.dumps(sdkResult.getData()));
                } else {
                    return GamePropGrantResult.failure(sdkResult.getCode(), sdkResult.getMessage());
                }
            } else {
                return GamePropGrantResult.failure(-1, "LUK SDK returned null result");
            }
            
        } catch (Exception e) {
            log.error("Error converting LUK result", e);
            return GamePropGrantResult.failure(-1, "Failed to convert LUK response: " + e.getMessage());
        }
    }
}
