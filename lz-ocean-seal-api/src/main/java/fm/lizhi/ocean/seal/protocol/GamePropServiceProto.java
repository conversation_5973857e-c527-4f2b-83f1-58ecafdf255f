// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_prop.proto

package fm.lizhi.ocean.seal.protocol;

public final class GamePropServiceProto {
  private GamePropServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface GamePropOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 id = 1;
    /**
     * <code>optional int64 id = 1;</code>
     */
    boolean hasId();
    /**
     * <code>optional int64 id = 1;</code>
     */
    long getId();

    // optional int64 channelGameId = 2;
    /**
     * <code>optional int64 channelGameId = 2;</code>
     */
    boolean hasChannelGameId();
    /**
     * <code>optional int64 channelGameId = 2;</code>
     */
    long getChannelGameId();

    // optional int64 channelPropId = 3;
    /**
     * <code>optional int64 channelPropId = 3;</code>
     */
    boolean hasChannelPropId();
    /**
     * <code>optional int64 channelPropId = 3;</code>
     */
    long getChannelPropId();

    // optional int64 appId = 4;
    /**
     * <code>optional int64 appId = 4;</code>
     */
    boolean hasAppId();
    /**
     * <code>optional int64 appId = 4;</code>
     */
    long getAppId();

    // optional int64 channelId = 5;
    /**
     * <code>optional int64 channelId = 5;</code>
     */
    boolean hasChannelId();
    /**
     * <code>optional int64 channelId = 5;</code>
     */
    long getChannelId();

    // optional string name = 6;
    /**
     * <code>optional string name = 6;</code>
     */
    boolean hasName();
    /**
     * <code>optional string name = 6;</code>
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 6;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    // optional string propDesc = 7;
    /**
     * <code>optional string propDesc = 7;</code>
     */
    boolean hasPropDesc();
    /**
     * <code>optional string propDesc = 7;</code>
     */
    java.lang.String getPropDesc();
    /**
     * <code>optional string propDesc = 7;</code>
     */
    com.google.protobuf.ByteString
        getPropDescBytes();

    // optional int32 type = 8;
    /**
     * <code>optional int32 type = 8;</code>
     *
     * <pre>
     * 道具类型, 1皮肤, 2道具
     * </pre>
     */
    boolean hasType();
    /**
     * <code>optional int32 type = 8;</code>
     *
     * <pre>
     * 道具类型, 1皮肤, 2道具
     * </pre>
     */
    int getType();

    // optional int32 durationSec = 9;
    /**
     * <code>optional int32 durationSec = 9;</code>
     *
     * <pre>
     * 有效时长（单位：秒），小于 0 为永久
     * </pre>
     */
    boolean hasDurationSec();
    /**
     * <code>optional int32 durationSec = 9;</code>
     *
     * <pre>
     * 有效时长（单位：秒），小于 0 为永久
     * </pre>
     */
    int getDurationSec();

    // optional bool timeliness = 10;
    /**
     * <code>optional bool timeliness = 10;</code>
     *
     * <pre>
     * 是否是时效性道具
     * </pre>
     */
    boolean hasTimeliness();
    /**
     * <code>optional bool timeliness = 10;</code>
     *
     * <pre>
     * 是否是时效性道具
     * </pre>
     */
    boolean getTimeliness();

    // optional string iconUrl = 11;
    /**
     * <code>optional string iconUrl = 11;</code>
     */
    boolean hasIconUrl();
    /**
     * <code>optional string iconUrl = 11;</code>
     */
    java.lang.String getIconUrl();
    /**
     * <code>optional string iconUrl = 11;</code>
     */
    com.google.protobuf.ByteString
        getIconUrlBytes();

    // optional string remark = 12;
    /**
     * <code>optional string remark = 12;</code>
     */
    boolean hasRemark();
    /**
     * <code>optional string remark = 12;</code>
     */
    java.lang.String getRemark();
    /**
     * <code>optional string remark = 12;</code>
     */
    com.google.protobuf.ByteString
        getRemarkBytes();

    // optional bool deleted = 13;
    /**
     * <code>optional bool deleted = 13;</code>
     */
    boolean hasDeleted();
    /**
     * <code>optional bool deleted = 13;</code>
     */
    boolean getDeleted();

    // optional string operator = 14;
    /**
     * <code>optional string operator = 14;</code>
     */
    boolean hasOperator();
    /**
     * <code>optional string operator = 14;</code>
     */
    java.lang.String getOperator();
    /**
     * <code>optional string operator = 14;</code>
     */
    com.google.protobuf.ByteString
        getOperatorBytes();

    // optional int64 createTime = 15;
    /**
     * <code>optional int64 createTime = 15;</code>
     */
    boolean hasCreateTime();
    /**
     * <code>optional int64 createTime = 15;</code>
     */
    long getCreateTime();

    // optional int64 modifyTime = 16;
    /**
     * <code>optional int64 modifyTime = 16;</code>
     */
    boolean hasModifyTime();
    /**
     * <code>optional int64 modifyTime = 16;</code>
     */
    long getModifyTime();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameProp}
   *
   * <pre>
   * 道具信息
   * </pre>
   */
  public static final class GameProp extends
      com.google.protobuf.GeneratedMessage
      implements GamePropOrBuilder {
    // Use GameProp.newBuilder() to construct.
    private GameProp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameProp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameProp defaultInstance;
    public static GameProp getDefaultInstance() {
      return defaultInstance;
    }

    public GameProp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameProp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              channelGameId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              channelPropId_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              appId_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              channelId_ = input.readInt64();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              name_ = input.readBytes();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000040;
              propDesc_ = input.readBytes();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              type_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              durationSec_ = input.readInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              timeliness_ = input.readBool();
              break;
            }
            case 90: {
              bitField0_ |= 0x00000400;
              iconUrl_ = input.readBytes();
              break;
            }
            case 98: {
              bitField0_ |= 0x00000800;
              remark_ = input.readBytes();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              deleted_ = input.readBool();
              break;
            }
            case 114: {
              bitField0_ |= 0x00002000;
              operator_ = input.readBytes();
              break;
            }
            case 120: {
              bitField0_ |= 0x00004000;
              createTime_ = input.readInt64();
              break;
            }
            case 128: {
              bitField0_ |= 0x00008000;
              modifyTime_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder.class);
    }

    public static com.google.protobuf.Parser<GameProp> PARSER =
        new com.google.protobuf.AbstractParser<GameProp>() {
      public GameProp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameProp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameProp> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <code>optional int64 id = 1;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }

    // optional int64 channelGameId = 2;
    public static final int CHANNELGAMEID_FIELD_NUMBER = 2;
    private long channelGameId_;
    /**
     * <code>optional int64 channelGameId = 2;</code>
     */
    public boolean hasChannelGameId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 channelGameId = 2;</code>
     */
    public long getChannelGameId() {
      return channelGameId_;
    }

    // optional int64 channelPropId = 3;
    public static final int CHANNELPROPID_FIELD_NUMBER = 3;
    private long channelPropId_;
    /**
     * <code>optional int64 channelPropId = 3;</code>
     */
    public boolean hasChannelPropId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 channelPropId = 3;</code>
     */
    public long getChannelPropId() {
      return channelPropId_;
    }

    // optional int64 appId = 4;
    public static final int APPID_FIELD_NUMBER = 4;
    private long appId_;
    /**
     * <code>optional int64 appId = 4;</code>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int64 appId = 4;</code>
     */
    public long getAppId() {
      return appId_;
    }

    // optional int64 channelId = 5;
    public static final int CHANNELID_FIELD_NUMBER = 5;
    private long channelId_;
    /**
     * <code>optional int64 channelId = 5;</code>
     */
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int64 channelId = 5;</code>
     */
    public long getChannelId() {
      return channelId_;
    }

    // optional string name = 6;
    public static final int NAME_FIELD_NUMBER = 6;
    private java.lang.Object name_;
    /**
     * <code>optional string name = 6;</code>
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional string name = 6;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 6;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string propDesc = 7;
    public static final int PROPDESC_FIELD_NUMBER = 7;
    private java.lang.Object propDesc_;
    /**
     * <code>optional string propDesc = 7;</code>
     */
    public boolean hasPropDesc() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional string propDesc = 7;</code>
     */
    public java.lang.String getPropDesc() {
      java.lang.Object ref = propDesc_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          propDesc_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string propDesc = 7;</code>
     */
    public com.google.protobuf.ByteString
        getPropDescBytes() {
      java.lang.Object ref = propDesc_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        propDesc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 type = 8;
    public static final int TYPE_FIELD_NUMBER = 8;
    private int type_;
    /**
     * <code>optional int32 type = 8;</code>
     *
     * <pre>
     * 道具类型, 1皮肤, 2道具
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional int32 type = 8;</code>
     *
     * <pre>
     * 道具类型, 1皮肤, 2道具
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // optional int32 durationSec = 9;
    public static final int DURATIONSEC_FIELD_NUMBER = 9;
    private int durationSec_;
    /**
     * <code>optional int32 durationSec = 9;</code>
     *
     * <pre>
     * 有效时长（单位：秒），小于 0 为永久
     * </pre>
     */
    public boolean hasDurationSec() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional int32 durationSec = 9;</code>
     *
     * <pre>
     * 有效时长（单位：秒），小于 0 为永久
     * </pre>
     */
    public int getDurationSec() {
      return durationSec_;
    }

    // optional bool timeliness = 10;
    public static final int TIMELINESS_FIELD_NUMBER = 10;
    private boolean timeliness_;
    /**
     * <code>optional bool timeliness = 10;</code>
     *
     * <pre>
     * 是否是时效性道具
     * </pre>
     */
    public boolean hasTimeliness() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional bool timeliness = 10;</code>
     *
     * <pre>
     * 是否是时效性道具
     * </pre>
     */
    public boolean getTimeliness() {
      return timeliness_;
    }

    // optional string iconUrl = 11;
    public static final int ICONURL_FIELD_NUMBER = 11;
    private java.lang.Object iconUrl_;
    /**
     * <code>optional string iconUrl = 11;</code>
     */
    public boolean hasIconUrl() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional string iconUrl = 11;</code>
     */
    public java.lang.String getIconUrl() {
      java.lang.Object ref = iconUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iconUrl_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string iconUrl = 11;</code>
     */
    public com.google.protobuf.ByteString
        getIconUrlBytes() {
      java.lang.Object ref = iconUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iconUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string remark = 12;
    public static final int REMARK_FIELD_NUMBER = 12;
    private java.lang.Object remark_;
    /**
     * <code>optional string remark = 12;</code>
     */
    public boolean hasRemark() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional string remark = 12;</code>
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          remark_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string remark = 12;</code>
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional bool deleted = 13;
    public static final int DELETED_FIELD_NUMBER = 13;
    private boolean deleted_;
    /**
     * <code>optional bool deleted = 13;</code>
     */
    public boolean hasDeleted() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bool deleted = 13;</code>
     */
    public boolean getDeleted() {
      return deleted_;
    }

    // optional string operator = 14;
    public static final int OPERATOR_FIELD_NUMBER = 14;
    private java.lang.Object operator_;
    /**
     * <code>optional string operator = 14;</code>
     */
    public boolean hasOperator() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional string operator = 14;</code>
     */
    public java.lang.String getOperator() {
      java.lang.Object ref = operator_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          operator_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string operator = 14;</code>
     */
    public com.google.protobuf.ByteString
        getOperatorBytes() {
      java.lang.Object ref = operator_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        operator_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 createTime = 15;
    public static final int CREATETIME_FIELD_NUMBER = 15;
    private long createTime_;
    /**
     * <code>optional int64 createTime = 15;</code>
     */
    public boolean hasCreateTime() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional int64 createTime = 15;</code>
     */
    public long getCreateTime() {
      return createTime_;
    }

    // optional int64 modifyTime = 16;
    public static final int MODIFYTIME_FIELD_NUMBER = 16;
    private long modifyTime_;
    /**
     * <code>optional int64 modifyTime = 16;</code>
     */
    public boolean hasModifyTime() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional int64 modifyTime = 16;</code>
     */
    public long getModifyTime() {
      return modifyTime_;
    }

    private void initFields() {
      id_ = 0L;
      channelGameId_ = 0L;
      channelPropId_ = 0L;
      appId_ = 0L;
      channelId_ = 0L;
      name_ = "";
      propDesc_ = "";
      type_ = 0;
      durationSec_ = 0;
      timeliness_ = false;
      iconUrl_ = "";
      remark_ = "";
      deleted_ = false;
      operator_ = "";
      createTime_ = 0L;
      modifyTime_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, channelGameId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, channelPropId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt64(4, appId_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt64(5, channelId_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, getNameBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(7, getPropDescBytes());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt32(8, type_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeInt32(9, durationSec_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeBool(10, timeliness_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(11, getIconUrlBytes());
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(12, getRemarkBytes());
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeBool(13, deleted_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(14, getOperatorBytes());
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeInt64(15, createTime_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeInt64(16, modifyTime_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, channelGameId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, channelPropId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, appId_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, channelId_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getNameBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, getPropDescBytes());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, type_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, durationSec_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(10, timeliness_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, getIconUrlBytes());
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, getRemarkBytes());
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(13, deleted_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(14, getOperatorBytes());
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(15, createTime_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(16, modifyTime_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameProp}
     *
     * <pre>
     * 道具信息
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        channelGameId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        channelPropId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        appId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        channelId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        propDesc_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        durationSec_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        timeliness_ = false;
        bitField0_ = (bitField0_ & ~0x00000200);
        iconUrl_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        remark_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        deleted_ = false;
        bitField0_ = (bitField0_ & ~0x00001000);
        operator_ = "";
        bitField0_ = (bitField0_ & ~0x00002000);
        createTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00004000);
        modifyTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00008000);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channelGameId_ = channelGameId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channelPropId_ = channelPropId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.channelId_ = channelId_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.propDesc_ = propDesc_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.durationSec_ = durationSec_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.timeliness_ = timeliness_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.iconUrl_ = iconUrl_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.remark_ = remark_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.deleted_ = deleted_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.operator_ = operator_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.createTime_ = createTime_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.modifyTime_ = modifyTime_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasChannelGameId()) {
          setChannelGameId(other.getChannelGameId());
        }
        if (other.hasChannelPropId()) {
          setChannelPropId(other.getChannelPropId());
        }
        if (other.hasAppId()) {
          setAppId(other.getAppId());
        }
        if (other.hasChannelId()) {
          setChannelId(other.getChannelId());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000020;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasPropDesc()) {
          bitField0_ |= 0x00000040;
          propDesc_ = other.propDesc_;
          onChanged();
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasDurationSec()) {
          setDurationSec(other.getDurationSec());
        }
        if (other.hasTimeliness()) {
          setTimeliness(other.getTimeliness());
        }
        if (other.hasIconUrl()) {
          bitField0_ |= 0x00000400;
          iconUrl_ = other.iconUrl_;
          onChanged();
        }
        if (other.hasRemark()) {
          bitField0_ |= 0x00000800;
          remark_ = other.remark_;
          onChanged();
        }
        if (other.hasDeleted()) {
          setDeleted(other.getDeleted());
        }
        if (other.hasOperator()) {
          bitField0_ |= 0x00002000;
          operator_ = other.operator_;
          onChanged();
        }
        if (other.hasCreateTime()) {
          setCreateTime(other.getCreateTime());
        }
        if (other.hasModifyTime()) {
          setModifyTime(other.getModifyTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 id = 1;
      private long id_ ;
      /**
       * <code>optional int64 id = 1;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public long getId() {
        return id_;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 channelGameId = 2;
      private long channelGameId_ ;
      /**
       * <code>optional int64 channelGameId = 2;</code>
       */
      public boolean hasChannelGameId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 channelGameId = 2;</code>
       */
      public long getChannelGameId() {
        return channelGameId_;
      }
      /**
       * <code>optional int64 channelGameId = 2;</code>
       */
      public Builder setChannelGameId(long value) {
        bitField0_ |= 0x00000002;
        channelGameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 channelGameId = 2;</code>
       */
      public Builder clearChannelGameId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channelGameId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 channelPropId = 3;
      private long channelPropId_ ;
      /**
       * <code>optional int64 channelPropId = 3;</code>
       */
      public boolean hasChannelPropId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 channelPropId = 3;</code>
       */
      public long getChannelPropId() {
        return channelPropId_;
      }
      /**
       * <code>optional int64 channelPropId = 3;</code>
       */
      public Builder setChannelPropId(long value) {
        bitField0_ |= 0x00000004;
        channelPropId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 channelPropId = 3;</code>
       */
      public Builder clearChannelPropId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channelPropId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 appId = 4;
      private long appId_ ;
      /**
       * <code>optional int64 appId = 4;</code>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int64 appId = 4;</code>
       */
      public long getAppId() {
        return appId_;
      }
      /**
       * <code>optional int64 appId = 4;</code>
       */
      public Builder setAppId(long value) {
        bitField0_ |= 0x00000008;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 appId = 4;</code>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        appId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 channelId = 5;
      private long channelId_ ;
      /**
       * <code>optional int64 channelId = 5;</code>
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int64 channelId = 5;</code>
       */
      public long getChannelId() {
        return channelId_;
      }
      /**
       * <code>optional int64 channelId = 5;</code>
       */
      public Builder setChannelId(long value) {
        bitField0_ |= 0x00000010;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 channelId = 5;</code>
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        channelId_ = 0L;
        onChanged();
        return this;
      }

      // optional string name = 6;
      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 6;</code>
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional string name = 6;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 6;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 6;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 6;</code>
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000020);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 6;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        name_ = value;
        onChanged();
        return this;
      }

      // optional string propDesc = 7;
      private java.lang.Object propDesc_ = "";
      /**
       * <code>optional string propDesc = 7;</code>
       */
      public boolean hasPropDesc() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional string propDesc = 7;</code>
       */
      public java.lang.String getPropDesc() {
        java.lang.Object ref = propDesc_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          propDesc_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string propDesc = 7;</code>
       */
      public com.google.protobuf.ByteString
          getPropDescBytes() {
        java.lang.Object ref = propDesc_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          propDesc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string propDesc = 7;</code>
       */
      public Builder setPropDesc(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        propDesc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string propDesc = 7;</code>
       */
      public Builder clearPropDesc() {
        bitField0_ = (bitField0_ & ~0x00000040);
        propDesc_ = getDefaultInstance().getPropDesc();
        onChanged();
        return this;
      }
      /**
       * <code>optional string propDesc = 7;</code>
       */
      public Builder setPropDescBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        propDesc_ = value;
        onChanged();
        return this;
      }

      // optional int32 type = 8;
      private int type_ ;
      /**
       * <code>optional int32 type = 8;</code>
       *
       * <pre>
       * 道具类型, 1皮肤, 2道具
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional int32 type = 8;</code>
       *
       * <pre>
       * 道具类型, 1皮肤, 2道具
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>optional int32 type = 8;</code>
       *
       * <pre>
       * 道具类型, 1皮肤, 2道具
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000080;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 type = 8;</code>
       *
       * <pre>
       * 道具类型, 1皮肤, 2道具
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000080);
        type_ = 0;
        onChanged();
        return this;
      }

      // optional int32 durationSec = 9;
      private int durationSec_ ;
      /**
       * <code>optional int32 durationSec = 9;</code>
       *
       * <pre>
       * 有效时长（单位：秒），小于 0 为永久
       * </pre>
       */
      public boolean hasDurationSec() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional int32 durationSec = 9;</code>
       *
       * <pre>
       * 有效时长（单位：秒），小于 0 为永久
       * </pre>
       */
      public int getDurationSec() {
        return durationSec_;
      }
      /**
       * <code>optional int32 durationSec = 9;</code>
       *
       * <pre>
       * 有效时长（单位：秒），小于 0 为永久
       * </pre>
       */
      public Builder setDurationSec(int value) {
        bitField0_ |= 0x00000100;
        durationSec_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 durationSec = 9;</code>
       *
       * <pre>
       * 有效时长（单位：秒），小于 0 为永久
       * </pre>
       */
      public Builder clearDurationSec() {
        bitField0_ = (bitField0_ & ~0x00000100);
        durationSec_ = 0;
        onChanged();
        return this;
      }

      // optional bool timeliness = 10;
      private boolean timeliness_ ;
      /**
       * <code>optional bool timeliness = 10;</code>
       *
       * <pre>
       * 是否是时效性道具
       * </pre>
       */
      public boolean hasTimeliness() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bool timeliness = 10;</code>
       *
       * <pre>
       * 是否是时效性道具
       * </pre>
       */
      public boolean getTimeliness() {
        return timeliness_;
      }
      /**
       * <code>optional bool timeliness = 10;</code>
       *
       * <pre>
       * 是否是时效性道具
       * </pre>
       */
      public Builder setTimeliness(boolean value) {
        bitField0_ |= 0x00000200;
        timeliness_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool timeliness = 10;</code>
       *
       * <pre>
       * 是否是时效性道具
       * </pre>
       */
      public Builder clearTimeliness() {
        bitField0_ = (bitField0_ & ~0x00000200);
        timeliness_ = false;
        onChanged();
        return this;
      }

      // optional string iconUrl = 11;
      private java.lang.Object iconUrl_ = "";
      /**
       * <code>optional string iconUrl = 11;</code>
       */
      public boolean hasIconUrl() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional string iconUrl = 11;</code>
       */
      public java.lang.String getIconUrl() {
        java.lang.Object ref = iconUrl_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          iconUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string iconUrl = 11;</code>
       */
      public com.google.protobuf.ByteString
          getIconUrlBytes() {
        java.lang.Object ref = iconUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iconUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string iconUrl = 11;</code>
       */
      public Builder setIconUrl(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        iconUrl_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string iconUrl = 11;</code>
       */
      public Builder clearIconUrl() {
        bitField0_ = (bitField0_ & ~0x00000400);
        iconUrl_ = getDefaultInstance().getIconUrl();
        onChanged();
        return this;
      }
      /**
       * <code>optional string iconUrl = 11;</code>
       */
      public Builder setIconUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        iconUrl_ = value;
        onChanged();
        return this;
      }

      // optional string remark = 12;
      private java.lang.Object remark_ = "";
      /**
       * <code>optional string remark = 12;</code>
       */
      public boolean hasRemark() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional string remark = 12;</code>
       */
      public java.lang.String getRemark() {
        java.lang.Object ref = remark_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          remark_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string remark = 12;</code>
       */
      public com.google.protobuf.ByteString
          getRemarkBytes() {
        java.lang.Object ref = remark_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          remark_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string remark = 12;</code>
       */
      public Builder setRemark(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        remark_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string remark = 12;</code>
       */
      public Builder clearRemark() {
        bitField0_ = (bitField0_ & ~0x00000800);
        remark_ = getDefaultInstance().getRemark();
        onChanged();
        return this;
      }
      /**
       * <code>optional string remark = 12;</code>
       */
      public Builder setRemarkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        remark_ = value;
        onChanged();
        return this;
      }

      // optional bool deleted = 13;
      private boolean deleted_ ;
      /**
       * <code>optional bool deleted = 13;</code>
       */
      public boolean hasDeleted() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bool deleted = 13;</code>
       */
      public boolean getDeleted() {
        return deleted_;
      }
      /**
       * <code>optional bool deleted = 13;</code>
       */
      public Builder setDeleted(boolean value) {
        bitField0_ |= 0x00001000;
        deleted_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool deleted = 13;</code>
       */
      public Builder clearDeleted() {
        bitField0_ = (bitField0_ & ~0x00001000);
        deleted_ = false;
        onChanged();
        return this;
      }

      // optional string operator = 14;
      private java.lang.Object operator_ = "";
      /**
       * <code>optional string operator = 14;</code>
       */
      public boolean hasOperator() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional string operator = 14;</code>
       */
      public java.lang.String getOperator() {
        java.lang.Object ref = operator_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          operator_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string operator = 14;</code>
       */
      public com.google.protobuf.ByteString
          getOperatorBytes() {
        java.lang.Object ref = operator_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          operator_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string operator = 14;</code>
       */
      public Builder setOperator(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        operator_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string operator = 14;</code>
       */
      public Builder clearOperator() {
        bitField0_ = (bitField0_ & ~0x00002000);
        operator_ = getDefaultInstance().getOperator();
        onChanged();
        return this;
      }
      /**
       * <code>optional string operator = 14;</code>
       */
      public Builder setOperatorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        operator_ = value;
        onChanged();
        return this;
      }

      // optional int64 createTime = 15;
      private long createTime_ ;
      /**
       * <code>optional int64 createTime = 15;</code>
       */
      public boolean hasCreateTime() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional int64 createTime = 15;</code>
       */
      public long getCreateTime() {
        return createTime_;
      }
      /**
       * <code>optional int64 createTime = 15;</code>
       */
      public Builder setCreateTime(long value) {
        bitField0_ |= 0x00004000;
        createTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 createTime = 15;</code>
       */
      public Builder clearCreateTime() {
        bitField0_ = (bitField0_ & ~0x00004000);
        createTime_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 modifyTime = 16;
      private long modifyTime_ ;
      /**
       * <code>optional int64 modifyTime = 16;</code>
       */
      public boolean hasModifyTime() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional int64 modifyTime = 16;</code>
       */
      public long getModifyTime() {
        return modifyTime_;
      }
      /**
       * <code>optional int64 modifyTime = 16;</code>
       */
      public Builder setModifyTime(long value) {
        bitField0_ |= 0x00008000;
        modifyTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 modifyTime = 16;</code>
       */
      public Builder clearModifyTime() {
        bitField0_ = (bitField0_ & ~0x00008000);
        modifyTime_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameProp)
    }

    static {
      defaultInstance = new GameProp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameProp)
  }

  public interface GamePropFlowOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 id = 1;
    /**
     * <code>optional int64 id = 1;</code>
     */
    boolean hasId();
    /**
     * <code>optional int64 id = 1;</code>
     */
    long getId();

    // optional int64 propId = 2;
    /**
     * <code>optional int64 propId = 2;</code>
     */
    boolean hasPropId();
    /**
     * <code>optional int64 propId = 2;</code>
     */
    long getPropId();

    // optional int64 userId = 3;
    /**
     * <code>optional int64 userId = 3;</code>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 3;</code>
     */
    long getUserId();

    // optional string uniqueId = 4;
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    boolean hasUniqueId();
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    java.lang.String getUniqueId();
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getUniqueIdBytes();

    // optional int32 durationSec = 5;
    /**
     * <code>optional int32 durationSec = 5;</code>
     *
     * <pre>
     * 有效时长（单位：秒），小于 0 代表永久
     * </pre>
     */
    boolean hasDurationSec();
    /**
     * <code>optional int32 durationSec = 5;</code>
     *
     * <pre>
     * 有效时长（单位：秒），小于 0 代表永久
     * </pre>
     */
    int getDurationSec();

    // optional int64 channelGameId = 6;
    /**
     * <code>optional int64 channelGameId = 6;</code>
     */
    boolean hasChannelGameId();
    /**
     * <code>optional int64 channelGameId = 6;</code>
     */
    long getChannelGameId();

    // optional int64 channelPropId = 7;
    /**
     * <code>optional int64 channelPropId = 7;</code>
     */
    boolean hasChannelPropId();
    /**
     * <code>optional int64 channelPropId = 7;</code>
     */
    long getChannelPropId();

    // optional int32 num = 8;
    /**
     * <code>optional int32 num = 8;</code>
     *
     * <pre>
     * 发放数量
     * </pre>
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 8;</code>
     *
     * <pre>
     * 发放数量
     * </pre>
     */
    int getNum();

    // optional int64 appId = 9;
    /**
     * <code>optional int64 appId = 9;</code>
     */
    boolean hasAppId();
    /**
     * <code>optional int64 appId = 9;</code>
     */
    long getAppId();

    // optional int32 type = 10;
    /**
     * <code>optional int32 type = 10;</code>
     *
     * <pre>
     * 道具类型, 1皮肤 2道具
     * </pre>
     */
    boolean hasType();
    /**
     * <code>optional int32 type = 10;</code>
     *
     * <pre>
     * 道具类型, 1皮肤 2道具
     * </pre>
     */
    int getType();

    // optional int32 grantStatus = 11;
    /**
     * <code>optional int32 grantStatus = 11;</code>
     *
     * <pre>
     * 发放状态，0未发放，1发放中，2成功，3失败
     * </pre>
     */
    boolean hasGrantStatus();
    /**
     * <code>optional int32 grantStatus = 11;</code>
     *
     * <pre>
     * 发放状态，0未发放，1发放中，2成功，3失败
     * </pre>
     */
    int getGrantStatus();

    // optional int64 createTime = 12;
    /**
     * <code>optional int64 createTime = 12;</code>
     */
    boolean hasCreateTime();
    /**
     * <code>optional int64 createTime = 12;</code>
     */
    long getCreateTime();

    // optional int64 modifyTime = 13;
    /**
     * <code>optional int64 modifyTime = 13;</code>
     */
    boolean hasModifyTime();
    /**
     * <code>optional int64 modifyTime = 13;</code>
     */
    long getModifyTime();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GamePropFlow}
   *
   * <pre>
   * 道具流水信息
   * </pre>
   */
  public static final class GamePropFlow extends
      com.google.protobuf.GeneratedMessage
      implements GamePropFlowOrBuilder {
    // Use GamePropFlow.newBuilder() to construct.
    private GamePropFlow(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GamePropFlow(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GamePropFlow defaultInstance;
    public static GamePropFlow getDefaultInstance() {
      return defaultInstance;
    }

    public GamePropFlow getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GamePropFlow(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              propId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              userId_ = input.readInt64();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              uniqueId_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              durationSec_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              channelGameId_ = input.readInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              channelPropId_ = input.readInt64();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              num_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              appId_ = input.readInt64();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              type_ = input.readInt32();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              grantStatus_ = input.readInt32();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              createTime_ = input.readInt64();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              modifyTime_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder.class);
    }

    public static com.google.protobuf.Parser<GamePropFlow> PARSER =
        new com.google.protobuf.AbstractParser<GamePropFlow>() {
      public GamePropFlow parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GamePropFlow(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GamePropFlow> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <code>optional int64 id = 1;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }

    // optional int64 propId = 2;
    public static final int PROPID_FIELD_NUMBER = 2;
    private long propId_;
    /**
     * <code>optional int64 propId = 2;</code>
     */
    public boolean hasPropId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 propId = 2;</code>
     */
    public long getPropId() {
      return propId_;
    }

    // optional int64 userId = 3;
    public static final int USERID_FIELD_NUMBER = 3;
    private long userId_;
    /**
     * <code>optional int64 userId = 3;</code>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 userId = 3;</code>
     */
    public long getUserId() {
      return userId_;
    }

    // optional string uniqueId = 4;
    public static final int UNIQUEID_FIELD_NUMBER = 4;
    private java.lang.Object uniqueId_;
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    public boolean hasUniqueId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    public java.lang.String getUniqueId() {
      java.lang.Object ref = uniqueId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          uniqueId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getUniqueIdBytes() {
      java.lang.Object ref = uniqueId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uniqueId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 durationSec = 5;
    public static final int DURATIONSEC_FIELD_NUMBER = 5;
    private int durationSec_;
    /**
     * <code>optional int32 durationSec = 5;</code>
     *
     * <pre>
     * 有效时长（单位：秒），小于 0 代表永久
     * </pre>
     */
    public boolean hasDurationSec() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 durationSec = 5;</code>
     *
     * <pre>
     * 有效时长（单位：秒），小于 0 代表永久
     * </pre>
     */
    public int getDurationSec() {
      return durationSec_;
    }

    // optional int64 channelGameId = 6;
    public static final int CHANNELGAMEID_FIELD_NUMBER = 6;
    private long channelGameId_;
    /**
     * <code>optional int64 channelGameId = 6;</code>
     */
    public boolean hasChannelGameId() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int64 channelGameId = 6;</code>
     */
    public long getChannelGameId() {
      return channelGameId_;
    }

    // optional int64 channelPropId = 7;
    public static final int CHANNELPROPID_FIELD_NUMBER = 7;
    private long channelPropId_;
    /**
     * <code>optional int64 channelPropId = 7;</code>
     */
    public boolean hasChannelPropId() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int64 channelPropId = 7;</code>
     */
    public long getChannelPropId() {
      return channelPropId_;
    }

    // optional int32 num = 8;
    public static final int NUM_FIELD_NUMBER = 8;
    private int num_;
    /**
     * <code>optional int32 num = 8;</code>
     *
     * <pre>
     * 发放数量
     * </pre>
     */
    public boolean hasNum() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional int32 num = 8;</code>
     *
     * <pre>
     * 发放数量
     * </pre>
     */
    public int getNum() {
      return num_;
    }

    // optional int64 appId = 9;
    public static final int APPID_FIELD_NUMBER = 9;
    private long appId_;
    /**
     * <code>optional int64 appId = 9;</code>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional int64 appId = 9;</code>
     */
    public long getAppId() {
      return appId_;
    }

    // optional int32 type = 10;
    public static final int TYPE_FIELD_NUMBER = 10;
    private int type_;
    /**
     * <code>optional int32 type = 10;</code>
     *
     * <pre>
     * 道具类型, 1皮肤 2道具
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional int32 type = 10;</code>
     *
     * <pre>
     * 道具类型, 1皮肤 2道具
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // optional int32 grantStatus = 11;
    public static final int GRANTSTATUS_FIELD_NUMBER = 11;
    private int grantStatus_;
    /**
     * <code>optional int32 grantStatus = 11;</code>
     *
     * <pre>
     * 发放状态，0未发放，1发放中，2成功，3失败
     * </pre>
     */
    public boolean hasGrantStatus() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional int32 grantStatus = 11;</code>
     *
     * <pre>
     * 发放状态，0未发放，1发放中，2成功，3失败
     * </pre>
     */
    public int getGrantStatus() {
      return grantStatus_;
    }

    // optional int64 createTime = 12;
    public static final int CREATETIME_FIELD_NUMBER = 12;
    private long createTime_;
    /**
     * <code>optional int64 createTime = 12;</code>
     */
    public boolean hasCreateTime() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional int64 createTime = 12;</code>
     */
    public long getCreateTime() {
      return createTime_;
    }

    // optional int64 modifyTime = 13;
    public static final int MODIFYTIME_FIELD_NUMBER = 13;
    private long modifyTime_;
    /**
     * <code>optional int64 modifyTime = 13;</code>
     */
    public boolean hasModifyTime() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional int64 modifyTime = 13;</code>
     */
    public long getModifyTime() {
      return modifyTime_;
    }

    private void initFields() {
      id_ = 0L;
      propId_ = 0L;
      userId_ = 0L;
      uniqueId_ = "";
      durationSec_ = 0;
      channelGameId_ = 0L;
      channelPropId_ = 0L;
      num_ = 0;
      appId_ = 0L;
      type_ = 0;
      grantStatus_ = 0;
      createTime_ = 0L;
      modifyTime_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, propId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, userId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getUniqueIdBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, durationSec_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt64(6, channelGameId_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt64(7, channelPropId_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt32(8, num_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeInt64(9, appId_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeInt32(10, type_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeInt32(11, grantStatus_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeInt64(12, createTime_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeInt64(13, modifyTime_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, propId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, userId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getUniqueIdBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, durationSec_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, channelGameId_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, channelPropId_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, num_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, appId_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, type_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, grantStatus_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(12, createTime_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(13, modifyTime_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GamePropFlow}
     *
     * <pre>
     * 道具流水信息
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        propId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        uniqueId_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        durationSec_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        channelGameId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        channelPropId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        appId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        grantStatus_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        createTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000800);
        modifyTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00001000);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.propId_ = propId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.uniqueId_ = uniqueId_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.durationSec_ = durationSec_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.channelGameId_ = channelGameId_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.channelPropId_ = channelPropId_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.num_ = num_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.grantStatus_ = grantStatus_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.createTime_ = createTime_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.modifyTime_ = modifyTime_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasPropId()) {
          setPropId(other.getPropId());
        }
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasUniqueId()) {
          bitField0_ |= 0x00000008;
          uniqueId_ = other.uniqueId_;
          onChanged();
        }
        if (other.hasDurationSec()) {
          setDurationSec(other.getDurationSec());
        }
        if (other.hasChannelGameId()) {
          setChannelGameId(other.getChannelGameId());
        }
        if (other.hasChannelPropId()) {
          setChannelPropId(other.getChannelPropId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        if (other.hasAppId()) {
          setAppId(other.getAppId());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasGrantStatus()) {
          setGrantStatus(other.getGrantStatus());
        }
        if (other.hasCreateTime()) {
          setCreateTime(other.getCreateTime());
        }
        if (other.hasModifyTime()) {
          setModifyTime(other.getModifyTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 id = 1;
      private long id_ ;
      /**
       * <code>optional int64 id = 1;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public long getId() {
        return id_;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 propId = 2;
      private long propId_ ;
      /**
       * <code>optional int64 propId = 2;</code>
       */
      public boolean hasPropId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 propId = 2;</code>
       */
      public long getPropId() {
        return propId_;
      }
      /**
       * <code>optional int64 propId = 2;</code>
       */
      public Builder setPropId(long value) {
        bitField0_ |= 0x00000002;
        propId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 propId = 2;</code>
       */
      public Builder clearPropId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        propId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 userId = 3;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 3;</code>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 userId = 3;</code>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 3;</code>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000004;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 3;</code>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional string uniqueId = 4;
      private java.lang.Object uniqueId_ = "";
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public boolean hasUniqueId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public java.lang.String getUniqueId() {
        java.lang.Object ref = uniqueId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          uniqueId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getUniqueIdBytes() {
        java.lang.Object ref = uniqueId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uniqueId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public Builder setUniqueId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        uniqueId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public Builder clearUniqueId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        uniqueId_ = getDefaultInstance().getUniqueId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public Builder setUniqueIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        uniqueId_ = value;
        onChanged();
        return this;
      }

      // optional int32 durationSec = 5;
      private int durationSec_ ;
      /**
       * <code>optional int32 durationSec = 5;</code>
       *
       * <pre>
       * 有效时长（单位：秒），小于 0 代表永久
       * </pre>
       */
      public boolean hasDurationSec() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 durationSec = 5;</code>
       *
       * <pre>
       * 有效时长（单位：秒），小于 0 代表永久
       * </pre>
       */
      public int getDurationSec() {
        return durationSec_;
      }
      /**
       * <code>optional int32 durationSec = 5;</code>
       *
       * <pre>
       * 有效时长（单位：秒），小于 0 代表永久
       * </pre>
       */
      public Builder setDurationSec(int value) {
        bitField0_ |= 0x00000010;
        durationSec_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 durationSec = 5;</code>
       *
       * <pre>
       * 有效时长（单位：秒），小于 0 代表永久
       * </pre>
       */
      public Builder clearDurationSec() {
        bitField0_ = (bitField0_ & ~0x00000010);
        durationSec_ = 0;
        onChanged();
        return this;
      }

      // optional int64 channelGameId = 6;
      private long channelGameId_ ;
      /**
       * <code>optional int64 channelGameId = 6;</code>
       */
      public boolean hasChannelGameId() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int64 channelGameId = 6;</code>
       */
      public long getChannelGameId() {
        return channelGameId_;
      }
      /**
       * <code>optional int64 channelGameId = 6;</code>
       */
      public Builder setChannelGameId(long value) {
        bitField0_ |= 0x00000020;
        channelGameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 channelGameId = 6;</code>
       */
      public Builder clearChannelGameId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        channelGameId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 channelPropId = 7;
      private long channelPropId_ ;
      /**
       * <code>optional int64 channelPropId = 7;</code>
       */
      public boolean hasChannelPropId() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int64 channelPropId = 7;</code>
       */
      public long getChannelPropId() {
        return channelPropId_;
      }
      /**
       * <code>optional int64 channelPropId = 7;</code>
       */
      public Builder setChannelPropId(long value) {
        bitField0_ |= 0x00000040;
        channelPropId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 channelPropId = 7;</code>
       */
      public Builder clearChannelPropId() {
        bitField0_ = (bitField0_ & ~0x00000040);
        channelPropId_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 num = 8;
      private int num_ ;
      /**
       * <code>optional int32 num = 8;</code>
       *
       * <pre>
       * 发放数量
       * </pre>
       */
      public boolean hasNum() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional int32 num = 8;</code>
       *
       * <pre>
       * 发放数量
       * </pre>
       */
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 8;</code>
       *
       * <pre>
       * 发放数量
       * </pre>
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000080;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 8;</code>
       *
       * <pre>
       * 发放数量
       * </pre>
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000080);
        num_ = 0;
        onChanged();
        return this;
      }

      // optional int64 appId = 9;
      private long appId_ ;
      /**
       * <code>optional int64 appId = 9;</code>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional int64 appId = 9;</code>
       */
      public long getAppId() {
        return appId_;
      }
      /**
       * <code>optional int64 appId = 9;</code>
       */
      public Builder setAppId(long value) {
        bitField0_ |= 0x00000100;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 appId = 9;</code>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        appId_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 type = 10;
      private int type_ ;
      /**
       * <code>optional int32 type = 10;</code>
       *
       * <pre>
       * 道具类型, 1皮肤 2道具
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional int32 type = 10;</code>
       *
       * <pre>
       * 道具类型, 1皮肤 2道具
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>optional int32 type = 10;</code>
       *
       * <pre>
       * 道具类型, 1皮肤 2道具
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000200;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 type = 10;</code>
       *
       * <pre>
       * 道具类型, 1皮肤 2道具
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000200);
        type_ = 0;
        onChanged();
        return this;
      }

      // optional int32 grantStatus = 11;
      private int grantStatus_ ;
      /**
       * <code>optional int32 grantStatus = 11;</code>
       *
       * <pre>
       * 发放状态，0未发放，1发放中，2成功，3失败
       * </pre>
       */
      public boolean hasGrantStatus() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional int32 grantStatus = 11;</code>
       *
       * <pre>
       * 发放状态，0未发放，1发放中，2成功，3失败
       * </pre>
       */
      public int getGrantStatus() {
        return grantStatus_;
      }
      /**
       * <code>optional int32 grantStatus = 11;</code>
       *
       * <pre>
       * 发放状态，0未发放，1发放中，2成功，3失败
       * </pre>
       */
      public Builder setGrantStatus(int value) {
        bitField0_ |= 0x00000400;
        grantStatus_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 grantStatus = 11;</code>
       *
       * <pre>
       * 发放状态，0未发放，1发放中，2成功，3失败
       * </pre>
       */
      public Builder clearGrantStatus() {
        bitField0_ = (bitField0_ & ~0x00000400);
        grantStatus_ = 0;
        onChanged();
        return this;
      }

      // optional int64 createTime = 12;
      private long createTime_ ;
      /**
       * <code>optional int64 createTime = 12;</code>
       */
      public boolean hasCreateTime() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional int64 createTime = 12;</code>
       */
      public long getCreateTime() {
        return createTime_;
      }
      /**
       * <code>optional int64 createTime = 12;</code>
       */
      public Builder setCreateTime(long value) {
        bitField0_ |= 0x00000800;
        createTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 createTime = 12;</code>
       */
      public Builder clearCreateTime() {
        bitField0_ = (bitField0_ & ~0x00000800);
        createTime_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 modifyTime = 13;
      private long modifyTime_ ;
      /**
       * <code>optional int64 modifyTime = 13;</code>
       */
      public boolean hasModifyTime() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional int64 modifyTime = 13;</code>
       */
      public long getModifyTime() {
        return modifyTime_;
      }
      /**
       * <code>optional int64 modifyTime = 13;</code>
       */
      public Builder setModifyTime(long value) {
        bitField0_ |= 0x00001000;
        modifyTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 modifyTime = 13;</code>
       */
      public Builder clearModifyTime() {
        bitField0_ = (bitField0_ & ~0x00001000);
        modifyTime_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GamePropFlow)
    }

    static {
      defaultInstance = new GamePropFlow(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GamePropFlow)
  }

  public interface GrantGamePropParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    long getUserId();

    // optional int64 propId = 2;
    /**
     * <code>optional int64 propId = 2;</code>
     *
     * <pre>
     * 道具ID
     * </pre>
     */
    boolean hasPropId();
    /**
     * <code>optional int64 propId = 2;</code>
     *
     * <pre>
     * 道具ID
     * </pre>
     */
    long getPropId();

    // optional int32 num = 3;
    /**
     * <code>optional int32 num = 3;</code>
     *
     * <pre>
     * 发放数量
     * </pre>
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 3;</code>
     *
     * <pre>
     * 发放数量
     * </pre>
     */
    int getNum();

    // optional string uniqueId = 4;
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    boolean hasUniqueId();
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    java.lang.String getUniqueId();
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getUniqueIdBytes();

    // optional string appId = 5;
    /**
     * <code>optional string appId = 5;</code>
     *
     * <pre>
     * 应用ID
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 5;</code>
     *
     * <pre>
     * 应用ID
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 5;</code>
     *
     * <pre>
     * 应用ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional string gameId = 6;
    /**
     * <code>optional string gameId = 6;</code>
     *
     * <pre>
     * 游戏ID
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>optional string gameId = 6;</code>
     *
     * <pre>
     * 游戏ID
     * </pre>
     */
    java.lang.String getGameId();
    /**
     * <code>optional string gameId = 6;</code>
     *
     * <pre>
     * 游戏ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameIdBytes();

    // optional string channel = 7;
    /**
     * <code>optional string channel = 7;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 7;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 7;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // optional int64 duration = 8;
    /**
     * <code>optional int64 duration = 8;</code>
     *
     * <pre>
     * 发放时长
     * </pre>
     */
    boolean hasDuration();
    /**
     * <code>optional int64 duration = 8;</code>
     *
     * <pre>
     * 发放时长
     * </pre>
     */
    long getDuration();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam}
   *
   * <pre>
   * 道具发放参数
   * </pre>
   */
  public static final class GrantGamePropParam extends
      com.google.protobuf.GeneratedMessage
      implements GrantGamePropParamOrBuilder {
    // Use GrantGamePropParam.newBuilder() to construct.
    private GrantGamePropParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GrantGamePropParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GrantGamePropParam defaultInstance;
    public static GrantGamePropParam getDefaultInstance() {
      return defaultInstance;
    }

    public GrantGamePropParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GrantGamePropParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              propId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              num_ = input.readInt32();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              uniqueId_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              appId_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              gameId_ = input.readBytes();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000040;
              channel_ = input.readBytes();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              duration_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.Builder.class);
    }

    public static com.google.protobuf.Parser<GrantGamePropParam> PARSER =
        new com.google.protobuf.AbstractParser<GrantGamePropParam>() {
      public GrantGamePropParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GrantGamePropParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GrantGamePropParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional int64 propId = 2;
    public static final int PROPID_FIELD_NUMBER = 2;
    private long propId_;
    /**
     * <code>optional int64 propId = 2;</code>
     *
     * <pre>
     * 道具ID
     * </pre>
     */
    public boolean hasPropId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 propId = 2;</code>
     *
     * <pre>
     * 道具ID
     * </pre>
     */
    public long getPropId() {
      return propId_;
    }

    // optional int32 num = 3;
    public static final int NUM_FIELD_NUMBER = 3;
    private int num_;
    /**
     * <code>optional int32 num = 3;</code>
     *
     * <pre>
     * 发放数量
     * </pre>
     */
    public boolean hasNum() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 num = 3;</code>
     *
     * <pre>
     * 发放数量
     * </pre>
     */
    public int getNum() {
      return num_;
    }

    // optional string uniqueId = 4;
    public static final int UNIQUEID_FIELD_NUMBER = 4;
    private java.lang.Object uniqueId_;
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    public boolean hasUniqueId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    public java.lang.String getUniqueId() {
      java.lang.Object ref = uniqueId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          uniqueId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string uniqueId = 4;</code>
     *
     * <pre>
     * 幂等的唯一 ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getUniqueIdBytes() {
      java.lang.Object ref = uniqueId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uniqueId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 5;
    public static final int APPID_FIELD_NUMBER = 5;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 5;</code>
     *
     * <pre>
     * 应用ID
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional string appId = 5;</code>
     *
     * <pre>
     * 应用ID
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 5;</code>
     *
     * <pre>
     * 应用ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string gameId = 6;
    public static final int GAMEID_FIELD_NUMBER = 6;
    private java.lang.Object gameId_;
    /**
     * <code>optional string gameId = 6;</code>
     *
     * <pre>
     * 游戏ID
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional string gameId = 6;</code>
     *
     * <pre>
     * 游戏ID
     * </pre>
     */
    public java.lang.String getGameId() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string gameId = 6;</code>
     *
     * <pre>
     * 游戏ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameIdBytes() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 7;
    public static final int CHANNEL_FIELD_NUMBER = 7;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 7;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional string channel = 7;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 7;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 duration = 8;
    public static final int DURATION_FIELD_NUMBER = 8;
    private long duration_;
    /**
     * <code>optional int64 duration = 8;</code>
     *
     * <pre>
     * 发放时长
     * </pre>
     */
    public boolean hasDuration() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional int64 duration = 8;</code>
     *
     * <pre>
     * 发放时长
     * </pre>
     */
    public long getDuration() {
      return duration_;
    }

    private void initFields() {
      userId_ = 0L;
      propId_ = 0L;
      num_ = 0;
      uniqueId_ = "";
      appId_ = "";
      gameId_ = "";
      channel_ = "";
      duration_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, propId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, num_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getUniqueIdBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(7, getChannelBytes());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt64(8, duration_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, propId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, num_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getUniqueIdBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, getChannelBytes());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, duration_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam}
     *
     * <pre>
     * 道具发放参数
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        propId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        uniqueId_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        gameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        duration_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.propId_ = propId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.num_ = num_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.uniqueId_ = uniqueId_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.gameId_ = gameId_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.duration_ = duration_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasPropId()) {
          setPropId(other.getPropId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        if (other.hasUniqueId()) {
          bitField0_ |= 0x00000008;
          uniqueId_ = other.uniqueId_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000010;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasGameId()) {
          bitField0_ |= 0x00000020;
          gameId_ = other.gameId_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000040;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasDuration()) {
          setDuration(other.getDuration());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 propId = 2;
      private long propId_ ;
      /**
       * <code>optional int64 propId = 2;</code>
       *
       * <pre>
       * 道具ID
       * </pre>
       */
      public boolean hasPropId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 propId = 2;</code>
       *
       * <pre>
       * 道具ID
       * </pre>
       */
      public long getPropId() {
        return propId_;
      }
      /**
       * <code>optional int64 propId = 2;</code>
       *
       * <pre>
       * 道具ID
       * </pre>
       */
      public Builder setPropId(long value) {
        bitField0_ |= 0x00000002;
        propId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 propId = 2;</code>
       *
       * <pre>
       * 道具ID
       * </pre>
       */
      public Builder clearPropId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        propId_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 num = 3;
      private int num_ ;
      /**
       * <code>optional int32 num = 3;</code>
       *
       * <pre>
       * 发放数量
       * </pre>
       */
      public boolean hasNum() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 num = 3;</code>
       *
       * <pre>
       * 发放数量
       * </pre>
       */
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 3;</code>
       *
       * <pre>
       * 发放数量
       * </pre>
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000004;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 3;</code>
       *
       * <pre>
       * 发放数量
       * </pre>
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        num_ = 0;
        onChanged();
        return this;
      }

      // optional string uniqueId = 4;
      private java.lang.Object uniqueId_ = "";
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public boolean hasUniqueId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public java.lang.String getUniqueId() {
        java.lang.Object ref = uniqueId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          uniqueId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getUniqueIdBytes() {
        java.lang.Object ref = uniqueId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uniqueId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public Builder setUniqueId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        uniqueId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public Builder clearUniqueId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        uniqueId_ = getDefaultInstance().getUniqueId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string uniqueId = 4;</code>
       *
       * <pre>
       * 幂等的唯一 ID
       * </pre>
       */
      public Builder setUniqueIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        uniqueId_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 5;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 5;</code>
       *
       * <pre>
       * 应用ID
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional string appId = 5;</code>
       *
       * <pre>
       * 应用ID
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 5;</code>
       *
       * <pre>
       * 应用ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 5;</code>
       *
       * <pre>
       * 应用ID
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 5;</code>
       *
       * <pre>
       * 应用ID
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 5;</code>
       *
       * <pre>
       * 应用ID
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional string gameId = 6;
      private java.lang.Object gameId_ = "";
      /**
       * <code>optional string gameId = 6;</code>
       *
       * <pre>
       * 游戏ID
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional string gameId = 6;</code>
       *
       * <pre>
       * 游戏ID
       * </pre>
       */
      public java.lang.String getGameId() {
        java.lang.Object ref = gameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string gameId = 6;</code>
       *
       * <pre>
       * 游戏ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameIdBytes() {
        java.lang.Object ref = gameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string gameId = 6;</code>
       *
       * <pre>
       * 游戏ID
       * </pre>
       */
      public Builder setGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string gameId = 6;</code>
       *
       * <pre>
       * 游戏ID
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        gameId_ = getDefaultInstance().getGameId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string gameId = 6;</code>
       *
       * <pre>
       * 游戏ID
       * </pre>
       */
      public Builder setGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        gameId_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 7;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 7;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional string channel = 7;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 7;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 7;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 7;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000040);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 7;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        channel_ = value;
        onChanged();
        return this;
      }

      // optional int64 duration = 8;
      private long duration_ ;
      /**
       * <code>optional int64 duration = 8;</code>
       *
       * <pre>
       * 发放时长
       * </pre>
       */
      public boolean hasDuration() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional int64 duration = 8;</code>
       *
       * <pre>
       * 发放时长
       * </pre>
       */
      public long getDuration() {
        return duration_;
      }
      /**
       * <code>optional int64 duration = 8;</code>
       *
       * <pre>
       * 发放时长
       * </pre>
       */
      public Builder setDuration(long value) {
        bitField0_ |= 0x00000080;
        duration_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 duration = 8;</code>
       *
       * <pre>
       * 发放时长
       * </pre>
       */
      public Builder clearDuration() {
        bitField0_ = (bitField0_ & ~0x00000080);
        duration_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam)
    }

    static {
      defaultInstance = new GrantGamePropParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam)
  }

  public interface GetGamePropListParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 channelGameId = 1;
    /**
     * <code>optional int64 channelGameId = 1;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    boolean hasChannelGameId();
    /**
     * <code>optional int64 channelGameId = 1;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    long getChannelGameId();

    // optional int64 channelId = 2;
    /**
     * <code>optional int64 channelId = 2;</code>
     *
     * <pre>
     * 渠道ID
     * </pre>
     */
    boolean hasChannelId();
    /**
     * <code>optional int64 channelId = 2;</code>
     *
     * <pre>
     * 渠道ID
     * </pre>
     */
    long getChannelId();

    // optional int32 type = 3;
    /**
     * <code>optional int32 type = 3;</code>
     *
     * <pre>
     * 道具类型
     * </pre>
     */
    boolean hasType();
    /**
     * <code>optional int32 type = 3;</code>
     *
     * <pre>
     * 道具类型
     * </pre>
     */
    int getType();

    // optional int32 pageNumber = 4;
    /**
     * <code>optional int32 pageNumber = 4;</code>
     *
     * <pre>
     * 页码
     * </pre>
     */
    boolean hasPageNumber();
    /**
     * <code>optional int32 pageNumber = 4;</code>
     *
     * <pre>
     * 页码
     * </pre>
     */
    int getPageNumber();

    // optional int32 pageSize = 5;
    /**
     * <code>optional int32 pageSize = 5;</code>
     *
     * <pre>
     * 页大小
     * </pre>
     */
    boolean hasPageSize();
    /**
     * <code>optional int32 pageSize = 5;</code>
     *
     * <pre>
     * 页大小
     * </pre>
     */
    int getPageSize();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam}
   *
   * <pre>
   * 道具列表查询参数
   * </pre>
   */
  public static final class GetGamePropListParam extends
      com.google.protobuf.GeneratedMessage
      implements GetGamePropListParamOrBuilder {
    // Use GetGamePropListParam.newBuilder() to construct.
    private GetGamePropListParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GetGamePropListParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GetGamePropListParam defaultInstance;
    public static GetGamePropListParam getDefaultInstance() {
      return defaultInstance;
    }

    public GetGamePropListParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GetGamePropListParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              channelGameId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              channelId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              type_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              pageNumber_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              pageSize_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.Builder.class);
    }

    public static com.google.protobuf.Parser<GetGamePropListParam> PARSER =
        new com.google.protobuf.AbstractParser<GetGamePropListParam>() {
      public GetGamePropListParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetGamePropListParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GetGamePropListParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 channelGameId = 1;
    public static final int CHANNELGAMEID_FIELD_NUMBER = 1;
    private long channelGameId_;
    /**
     * <code>optional int64 channelGameId = 1;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    public boolean hasChannelGameId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 channelGameId = 1;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    public long getChannelGameId() {
      return channelGameId_;
    }

    // optional int64 channelId = 2;
    public static final int CHANNELID_FIELD_NUMBER = 2;
    private long channelId_;
    /**
     * <code>optional int64 channelId = 2;</code>
     *
     * <pre>
     * 渠道ID
     * </pre>
     */
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 channelId = 2;</code>
     *
     * <pre>
     * 渠道ID
     * </pre>
     */
    public long getChannelId() {
      return channelId_;
    }

    // optional int32 type = 3;
    public static final int TYPE_FIELD_NUMBER = 3;
    private int type_;
    /**
     * <code>optional int32 type = 3;</code>
     *
     * <pre>
     * 道具类型
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 type = 3;</code>
     *
     * <pre>
     * 道具类型
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // optional int32 pageNumber = 4;
    public static final int PAGENUMBER_FIELD_NUMBER = 4;
    private int pageNumber_;
    /**
     * <code>optional int32 pageNumber = 4;</code>
     *
     * <pre>
     * 页码
     * </pre>
     */
    public boolean hasPageNumber() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 pageNumber = 4;</code>
     *
     * <pre>
     * 页码
     * </pre>
     */
    public int getPageNumber() {
      return pageNumber_;
    }

    // optional int32 pageSize = 5;
    public static final int PAGESIZE_FIELD_NUMBER = 5;
    private int pageSize_;
    /**
     * <code>optional int32 pageSize = 5;</code>
     *
     * <pre>
     * 页大小
     * </pre>
     */
    public boolean hasPageSize() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 pageSize = 5;</code>
     *
     * <pre>
     * 页大小
     * </pre>
     */
    public int getPageSize() {
      return pageSize_;
    }

    private void initFields() {
      channelGameId_ = 0L;
      channelId_ = 0L;
      type_ = 0;
      pageNumber_ = 0;
      pageSize_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, channelGameId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, channelId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, type_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, pageNumber_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, pageSize_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, channelGameId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, channelId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, type_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, pageNumber_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, pageSize_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam}
     *
     * <pre>
     * 道具列表查询参数
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        channelGameId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        channelId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        pageNumber_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        pageSize_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelGameId_ = channelGameId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channelId_ = channelId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.pageNumber_ = pageNumber_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.pageSize_ = pageSize_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.getDefaultInstance()) return this;
        if (other.hasChannelGameId()) {
          setChannelGameId(other.getChannelGameId());
        }
        if (other.hasChannelId()) {
          setChannelId(other.getChannelId());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasPageNumber()) {
          setPageNumber(other.getPageNumber());
        }
        if (other.hasPageSize()) {
          setPageSize(other.getPageSize());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 channelGameId = 1;
      private long channelGameId_ ;
      /**
       * <code>optional int64 channelGameId = 1;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public boolean hasChannelGameId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 channelGameId = 1;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public long getChannelGameId() {
        return channelGameId_;
      }
      /**
       * <code>optional int64 channelGameId = 1;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public Builder setChannelGameId(long value) {
        bitField0_ |= 0x00000001;
        channelGameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 channelGameId = 1;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public Builder clearChannelGameId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelGameId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 channelId = 2;
      private long channelId_ ;
      /**
       * <code>optional int64 channelId = 2;</code>
       *
       * <pre>
       * 渠道ID
       * </pre>
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 channelId = 2;</code>
       *
       * <pre>
       * 渠道ID
       * </pre>
       */
      public long getChannelId() {
        return channelId_;
      }
      /**
       * <code>optional int64 channelId = 2;</code>
       *
       * <pre>
       * 渠道ID
       * </pre>
       */
      public Builder setChannelId(long value) {
        bitField0_ |= 0x00000002;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 channelId = 2;</code>
       *
       * <pre>
       * 渠道ID
       * </pre>
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channelId_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 type = 3;
      private int type_ ;
      /**
       * <code>optional int32 type = 3;</code>
       *
       * <pre>
       * 道具类型
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 type = 3;</code>
       *
       * <pre>
       * 道具类型
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>optional int32 type = 3;</code>
       *
       * <pre>
       * 道具类型
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000004;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 type = 3;</code>
       *
       * <pre>
       * 道具类型
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        type_ = 0;
        onChanged();
        return this;
      }

      // optional int32 pageNumber = 4;
      private int pageNumber_ ;
      /**
       * <code>optional int32 pageNumber = 4;</code>
       *
       * <pre>
       * 页码
       * </pre>
       */
      public boolean hasPageNumber() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 pageNumber = 4;</code>
       *
       * <pre>
       * 页码
       * </pre>
       */
      public int getPageNumber() {
        return pageNumber_;
      }
      /**
       * <code>optional int32 pageNumber = 4;</code>
       *
       * <pre>
       * 页码
       * </pre>
       */
      public Builder setPageNumber(int value) {
        bitField0_ |= 0x00000008;
        pageNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageNumber = 4;</code>
       *
       * <pre>
       * 页码
       * </pre>
       */
      public Builder clearPageNumber() {
        bitField0_ = (bitField0_ & ~0x00000008);
        pageNumber_ = 0;
        onChanged();
        return this;
      }

      // optional int32 pageSize = 5;
      private int pageSize_ ;
      /**
       * <code>optional int32 pageSize = 5;</code>
       *
       * <pre>
       * 页大小
       * </pre>
       */
      public boolean hasPageSize() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 pageSize = 5;</code>
       *
       * <pre>
       * 页大小
       * </pre>
       */
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <code>optional int32 pageSize = 5;</code>
       *
       * <pre>
       * 页大小
       * </pre>
       */
      public Builder setPageSize(int value) {
        bitField0_ |= 0x00000010;
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageSize = 5;</code>
       *
       * <pre>
       * 页大小
       * </pre>
       */
      public Builder clearPageSize() {
        bitField0_ = (bitField0_ & ~0x00000010);
        pageSize_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam)
    }

    static {
      defaultInstance = new GetGamePropListParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam)
  }

  public interface GetGamePropFlowListParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    long getUserId();

    // optional int64 propId = 2;
    /**
     * <code>optional int64 propId = 2;</code>
     *
     * <pre>
     * 道具ID
     * </pre>
     */
    boolean hasPropId();
    /**
     * <code>optional int64 propId = 2;</code>
     *
     * <pre>
     * 道具ID
     * </pre>
     */
    long getPropId();

    // optional int32 grantStatus = 3;
    /**
     * <code>optional int32 grantStatus = 3;</code>
     *
     * <pre>
     * 发放状态
     * </pre>
     */
    boolean hasGrantStatus();
    /**
     * <code>optional int32 grantStatus = 3;</code>
     *
     * <pre>
     * 发放状态
     * </pre>
     */
    int getGrantStatus();

    // optional int64 startTime = 4;
    /**
     * <code>optional int64 startTime = 4;</code>
     *
     * <pre>
     * 开始时间
     * </pre>
     */
    boolean hasStartTime();
    /**
     * <code>optional int64 startTime = 4;</code>
     *
     * <pre>
     * 开始时间
     * </pre>
     */
    long getStartTime();

    // optional int64 endTime = 5;
    /**
     * <code>optional int64 endTime = 5;</code>
     *
     * <pre>
     * 结束时间
     * </pre>
     */
    boolean hasEndTime();
    /**
     * <code>optional int64 endTime = 5;</code>
     *
     * <pre>
     * 结束时间
     * </pre>
     */
    long getEndTime();

    // optional int32 pageNumber = 6;
    /**
     * <code>optional int32 pageNumber = 6;</code>
     *
     * <pre>
     * 页码
     * </pre>
     */
    boolean hasPageNumber();
    /**
     * <code>optional int32 pageNumber = 6;</code>
     *
     * <pre>
     * 页码
     * </pre>
     */
    int getPageNumber();

    // optional int32 pageSize = 7;
    /**
     * <code>optional int32 pageSize = 7;</code>
     *
     * <pre>
     * 页大小
     * </pre>
     */
    boolean hasPageSize();
    /**
     * <code>optional int32 pageSize = 7;</code>
     *
     * <pre>
     * 页大小
     * </pre>
     */
    int getPageSize();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam}
   *
   * <pre>
   * 道具流水查询参数
   * </pre>
   */
  public static final class GetGamePropFlowListParam extends
      com.google.protobuf.GeneratedMessage
      implements GetGamePropFlowListParamOrBuilder {
    // Use GetGamePropFlowListParam.newBuilder() to construct.
    private GetGamePropFlowListParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GetGamePropFlowListParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GetGamePropFlowListParam defaultInstance;
    public static GetGamePropFlowListParam getDefaultInstance() {
      return defaultInstance;
    }

    public GetGamePropFlowListParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GetGamePropFlowListParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              propId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              grantStatus_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              startTime_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              endTime_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              pageNumber_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              pageSize_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.Builder.class);
    }

    public static com.google.protobuf.Parser<GetGamePropFlowListParam> PARSER =
        new com.google.protobuf.AbstractParser<GetGamePropFlowListParam>() {
      public GetGamePropFlowListParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetGamePropFlowListParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GetGamePropFlowListParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional int64 propId = 2;
    public static final int PROPID_FIELD_NUMBER = 2;
    private long propId_;
    /**
     * <code>optional int64 propId = 2;</code>
     *
     * <pre>
     * 道具ID
     * </pre>
     */
    public boolean hasPropId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 propId = 2;</code>
     *
     * <pre>
     * 道具ID
     * </pre>
     */
    public long getPropId() {
      return propId_;
    }

    // optional int32 grantStatus = 3;
    public static final int GRANTSTATUS_FIELD_NUMBER = 3;
    private int grantStatus_;
    /**
     * <code>optional int32 grantStatus = 3;</code>
     *
     * <pre>
     * 发放状态
     * </pre>
     */
    public boolean hasGrantStatus() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 grantStatus = 3;</code>
     *
     * <pre>
     * 发放状态
     * </pre>
     */
    public int getGrantStatus() {
      return grantStatus_;
    }

    // optional int64 startTime = 4;
    public static final int STARTTIME_FIELD_NUMBER = 4;
    private long startTime_;
    /**
     * <code>optional int64 startTime = 4;</code>
     *
     * <pre>
     * 开始时间
     * </pre>
     */
    public boolean hasStartTime() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int64 startTime = 4;</code>
     *
     * <pre>
     * 开始时间
     * </pre>
     */
    public long getStartTime() {
      return startTime_;
    }

    // optional int64 endTime = 5;
    public static final int ENDTIME_FIELD_NUMBER = 5;
    private long endTime_;
    /**
     * <code>optional int64 endTime = 5;</code>
     *
     * <pre>
     * 结束时间
     * </pre>
     */
    public boolean hasEndTime() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int64 endTime = 5;</code>
     *
     * <pre>
     * 结束时间
     * </pre>
     */
    public long getEndTime() {
      return endTime_;
    }

    // optional int32 pageNumber = 6;
    public static final int PAGENUMBER_FIELD_NUMBER = 6;
    private int pageNumber_;
    /**
     * <code>optional int32 pageNumber = 6;</code>
     *
     * <pre>
     * 页码
     * </pre>
     */
    public boolean hasPageNumber() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int32 pageNumber = 6;</code>
     *
     * <pre>
     * 页码
     * </pre>
     */
    public int getPageNumber() {
      return pageNumber_;
    }

    // optional int32 pageSize = 7;
    public static final int PAGESIZE_FIELD_NUMBER = 7;
    private int pageSize_;
    /**
     * <code>optional int32 pageSize = 7;</code>
     *
     * <pre>
     * 页大小
     * </pre>
     */
    public boolean hasPageSize() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int32 pageSize = 7;</code>
     *
     * <pre>
     * 页大小
     * </pre>
     */
    public int getPageSize() {
      return pageSize_;
    }

    private void initFields() {
      userId_ = 0L;
      propId_ = 0L;
      grantStatus_ = 0;
      startTime_ = 0L;
      endTime_ = 0L;
      pageNumber_ = 0;
      pageSize_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, propId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, grantStatus_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt64(4, startTime_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt64(5, endTime_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(6, pageNumber_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, pageSize_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, propId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, grantStatus_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, startTime_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, endTime_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, pageNumber_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, pageSize_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam}
     *
     * <pre>
     * 道具流水查询参数
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        propId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        grantStatus_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        startTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        endTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        pageNumber_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        pageSize_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.propId_ = propId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.grantStatus_ = grantStatus_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.startTime_ = startTime_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.endTime_ = endTime_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.pageNumber_ = pageNumber_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.pageSize_ = pageSize_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasPropId()) {
          setPropId(other.getPropId());
        }
        if (other.hasGrantStatus()) {
          setGrantStatus(other.getGrantStatus());
        }
        if (other.hasStartTime()) {
          setStartTime(other.getStartTime());
        }
        if (other.hasEndTime()) {
          setEndTime(other.getEndTime());
        }
        if (other.hasPageNumber()) {
          setPageNumber(other.getPageNumber());
        }
        if (other.hasPageSize()) {
          setPageSize(other.getPageSize());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 propId = 2;
      private long propId_ ;
      /**
       * <code>optional int64 propId = 2;</code>
       *
       * <pre>
       * 道具ID
       * </pre>
       */
      public boolean hasPropId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 propId = 2;</code>
       *
       * <pre>
       * 道具ID
       * </pre>
       */
      public long getPropId() {
        return propId_;
      }
      /**
       * <code>optional int64 propId = 2;</code>
       *
       * <pre>
       * 道具ID
       * </pre>
       */
      public Builder setPropId(long value) {
        bitField0_ |= 0x00000002;
        propId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 propId = 2;</code>
       *
       * <pre>
       * 道具ID
       * </pre>
       */
      public Builder clearPropId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        propId_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 grantStatus = 3;
      private int grantStatus_ ;
      /**
       * <code>optional int32 grantStatus = 3;</code>
       *
       * <pre>
       * 发放状态
       * </pre>
       */
      public boolean hasGrantStatus() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 grantStatus = 3;</code>
       *
       * <pre>
       * 发放状态
       * </pre>
       */
      public int getGrantStatus() {
        return grantStatus_;
      }
      /**
       * <code>optional int32 grantStatus = 3;</code>
       *
       * <pre>
       * 发放状态
       * </pre>
       */
      public Builder setGrantStatus(int value) {
        bitField0_ |= 0x00000004;
        grantStatus_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 grantStatus = 3;</code>
       *
       * <pre>
       * 发放状态
       * </pre>
       */
      public Builder clearGrantStatus() {
        bitField0_ = (bitField0_ & ~0x00000004);
        grantStatus_ = 0;
        onChanged();
        return this;
      }

      // optional int64 startTime = 4;
      private long startTime_ ;
      /**
       * <code>optional int64 startTime = 4;</code>
       *
       * <pre>
       * 开始时间
       * </pre>
       */
      public boolean hasStartTime() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int64 startTime = 4;</code>
       *
       * <pre>
       * 开始时间
       * </pre>
       */
      public long getStartTime() {
        return startTime_;
      }
      /**
       * <code>optional int64 startTime = 4;</code>
       *
       * <pre>
       * 开始时间
       * </pre>
       */
      public Builder setStartTime(long value) {
        bitField0_ |= 0x00000008;
        startTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 startTime = 4;</code>
       *
       * <pre>
       * 开始时间
       * </pre>
       */
      public Builder clearStartTime() {
        bitField0_ = (bitField0_ & ~0x00000008);
        startTime_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 endTime = 5;
      private long endTime_ ;
      /**
       * <code>optional int64 endTime = 5;</code>
       *
       * <pre>
       * 结束时间
       * </pre>
       */
      public boolean hasEndTime() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int64 endTime = 5;</code>
       *
       * <pre>
       * 结束时间
       * </pre>
       */
      public long getEndTime() {
        return endTime_;
      }
      /**
       * <code>optional int64 endTime = 5;</code>
       *
       * <pre>
       * 结束时间
       * </pre>
       */
      public Builder setEndTime(long value) {
        bitField0_ |= 0x00000010;
        endTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 endTime = 5;</code>
       *
       * <pre>
       * 结束时间
       * </pre>
       */
      public Builder clearEndTime() {
        bitField0_ = (bitField0_ & ~0x00000010);
        endTime_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 pageNumber = 6;
      private int pageNumber_ ;
      /**
       * <code>optional int32 pageNumber = 6;</code>
       *
       * <pre>
       * 页码
       * </pre>
       */
      public boolean hasPageNumber() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int32 pageNumber = 6;</code>
       *
       * <pre>
       * 页码
       * </pre>
       */
      public int getPageNumber() {
        return pageNumber_;
      }
      /**
       * <code>optional int32 pageNumber = 6;</code>
       *
       * <pre>
       * 页码
       * </pre>
       */
      public Builder setPageNumber(int value) {
        bitField0_ |= 0x00000020;
        pageNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageNumber = 6;</code>
       *
       * <pre>
       * 页码
       * </pre>
       */
      public Builder clearPageNumber() {
        bitField0_ = (bitField0_ & ~0x00000020);
        pageNumber_ = 0;
        onChanged();
        return this;
      }

      // optional int32 pageSize = 7;
      private int pageSize_ ;
      /**
       * <code>optional int32 pageSize = 7;</code>
       *
       * <pre>
       * 页大小
       * </pre>
       */
      public boolean hasPageSize() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int32 pageSize = 7;</code>
       *
       * <pre>
       * 页大小
       * </pre>
       */
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <code>optional int32 pageSize = 7;</code>
       *
       * <pre>
       * 页大小
       * </pre>
       */
      public Builder setPageSize(int value) {
        bitField0_ |= 0x00000040;
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageSize = 7;</code>
       *
       * <pre>
       * 页大小
       * </pre>
       */
      public Builder clearPageSize() {
        bitField0_ = (bitField0_ & ~0x00000040);
        pageSize_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam)
    }

    static {
      defaultInstance = new GetGamePropFlowListParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam)
  }

  public interface RequestGrantGamePropOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGrantGameProp}
   *
   * <pre>
   * GamePropService.java
   * 发放游戏道具/皮肤
   * domain = 4302, op = 260
   * </pre>
   */
  public static final class RequestGrantGameProp extends
      com.google.protobuf.GeneratedMessage
      implements RequestGrantGamePropOrBuilder {
    // Use RequestGrantGameProp.newBuilder() to construct.
    private RequestGrantGameProp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGrantGameProp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGrantGameProp defaultInstance;
    public static RequestGrantGameProp getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGrantGameProp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGrantGameProp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGrantGameProp> PARSER =
        new com.google.protobuf.AbstractParser<RequestGrantGameProp>() {
      public RequestGrantGameProp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGrantGameProp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGrantGameProp> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGrantGameProp}
     *
     * <pre>
     * GamePropService.java
     * 发放游戏道具/皮肤
     * domain = 4302, op = 260
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGamePropOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GrantGamePropParam param = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGrantGameProp)
    }

    static {
      defaultInstance = new RequestGrantGameProp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGrantGameProp)
  }

  public interface ResponseGrantGamePropOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int32 grantStatus = 1;
    /**
     * <code>optional int32 grantStatus = 1;</code>
     *
     * <pre>
     * 发放状态，0未发放，1发放中，2成功，3失败
     * </pre>
     */
    boolean hasGrantStatus();
    /**
     * <code>optional int32 grantStatus = 1;</code>
     *
     * <pre>
     * 发放状态，0未发放，1发放中，2成功，3失败
     * </pre>
     */
    int getGrantStatus();

    // optional string message = 2;
    /**
     * <code>optional string message = 2;</code>
     *
     * <pre>
     * 消息
     * </pre>
     */
    boolean hasMessage();
    /**
     * <code>optional string message = 2;</code>
     *
     * <pre>
     * 消息
     * </pre>
     */
    java.lang.String getMessage();
    /**
     * <code>optional string message = 2;</code>
     *
     * <pre>
     * 消息
     * </pre>
     */
    com.google.protobuf.ByteString
        getMessageBytes();

    // optional int64 flowId = 3;
    /**
     * <code>optional int64 flowId = 3;</code>
     *
     * <pre>
     * 流水ID
     * </pre>
     */
    boolean hasFlowId();
    /**
     * <code>optional int64 flowId = 3;</code>
     *
     * <pre>
     * 流水ID
     * </pre>
     */
    long getFlowId();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGrantGameProp}
   *
   * <pre>
   * rcode == 0 (SUCCESS) = 发放成功
   * rcode == 1 (PARAM_ERROR) = 参数错误
   * rcode == 2 (NOT_EXISTS) = 道具不存在
   * rcode == 3 (USER_NOT_EXISTS) = 用户不存在
   * rcode == 4 (FAIL) = 发放失败
   * rcode == 5 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGrantGameProp extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGrantGamePropOrBuilder {
    // Use ResponseGrantGameProp.newBuilder() to construct.
    private ResponseGrantGameProp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGrantGameProp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGrantGameProp defaultInstance;
    public static ResponseGrantGameProp getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGrantGameProp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGrantGameProp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              grantStatus_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              message_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              flowId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGrantGameProp> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGrantGameProp>() {
      public ResponseGrantGameProp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGrantGameProp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGrantGameProp> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int32 grantStatus = 1;
    public static final int GRANTSTATUS_FIELD_NUMBER = 1;
    private int grantStatus_;
    /**
     * <code>optional int32 grantStatus = 1;</code>
     *
     * <pre>
     * 发放状态，0未发放，1发放中，2成功，3失败
     * </pre>
     */
    public boolean hasGrantStatus() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 grantStatus = 1;</code>
     *
     * <pre>
     * 发放状态，0未发放，1发放中，2成功，3失败
     * </pre>
     */
    public int getGrantStatus() {
      return grantStatus_;
    }

    // optional string message = 2;
    public static final int MESSAGE_FIELD_NUMBER = 2;
    private java.lang.Object message_;
    /**
     * <code>optional string message = 2;</code>
     *
     * <pre>
     * 消息
     * </pre>
     */
    public boolean hasMessage() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string message = 2;</code>
     *
     * <pre>
     * 消息
     * </pre>
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          message_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string message = 2;</code>
     *
     * <pre>
     * 消息
     * </pre>
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 flowId = 3;
    public static final int FLOWID_FIELD_NUMBER = 3;
    private long flowId_;
    /**
     * <code>optional int64 flowId = 3;</code>
     *
     * <pre>
     * 流水ID
     * </pre>
     */
    public boolean hasFlowId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 flowId = 3;</code>
     *
     * <pre>
     * 流水ID
     * </pre>
     */
    public long getFlowId() {
      return flowId_;
    }

    private void initFields() {
      grantStatus_ = 0;
      message_ = "";
      flowId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, grantStatus_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getMessageBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, flowId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, grantStatus_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getMessageBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, flowId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGrantGameProp}
     *
     * <pre>
     * rcode == 0 (SUCCESS) = 发放成功
     * rcode == 1 (PARAM_ERROR) = 参数错误
     * rcode == 2 (NOT_EXISTS) = 道具不存在
     * rcode == 3 (USER_NOT_EXISTS) = 用户不存在
     * rcode == 4 (FAIL) = 发放失败
     * rcode == 5 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGamePropOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        grantStatus_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        message_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        flowId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.grantStatus_ = grantStatus_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.message_ = message_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.flowId_ = flowId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp.getDefaultInstance()) return this;
        if (other.hasGrantStatus()) {
          setGrantStatus(other.getGrantStatus());
        }
        if (other.hasMessage()) {
          bitField0_ |= 0x00000002;
          message_ = other.message_;
          onChanged();
        }
        if (other.hasFlowId()) {
          setFlowId(other.getFlowId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int32 grantStatus = 1;
      private int grantStatus_ ;
      /**
       * <code>optional int32 grantStatus = 1;</code>
       *
       * <pre>
       * 发放状态，0未发放，1发放中，2成功，3失败
       * </pre>
       */
      public boolean hasGrantStatus() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int32 grantStatus = 1;</code>
       *
       * <pre>
       * 发放状态，0未发放，1发放中，2成功，3失败
       * </pre>
       */
      public int getGrantStatus() {
        return grantStatus_;
      }
      /**
       * <code>optional int32 grantStatus = 1;</code>
       *
       * <pre>
       * 发放状态，0未发放，1发放中，2成功，3失败
       * </pre>
       */
      public Builder setGrantStatus(int value) {
        bitField0_ |= 0x00000001;
        grantStatus_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 grantStatus = 1;</code>
       *
       * <pre>
       * 发放状态，0未发放，1发放中，2成功，3失败
       * </pre>
       */
      public Builder clearGrantStatus() {
        bitField0_ = (bitField0_ & ~0x00000001);
        grantStatus_ = 0;
        onChanged();
        return this;
      }

      // optional string message = 2;
      private java.lang.Object message_ = "";
      /**
       * <code>optional string message = 2;</code>
       *
       * <pre>
       * 消息
       * </pre>
       */
      public boolean hasMessage() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string message = 2;</code>
       *
       * <pre>
       * 消息
       * </pre>
       */
      public java.lang.String getMessage() {
        java.lang.Object ref = message_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          message_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string message = 2;</code>
       *
       * <pre>
       * 消息
       * </pre>
       */
      public com.google.protobuf.ByteString
          getMessageBytes() {
        java.lang.Object ref = message_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          message_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string message = 2;</code>
       *
       * <pre>
       * 消息
       * </pre>
       */
      public Builder setMessage(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        message_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string message = 2;</code>
       *
       * <pre>
       * 消息
       * </pre>
       */
      public Builder clearMessage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        message_ = getDefaultInstance().getMessage();
        onChanged();
        return this;
      }
      /**
       * <code>optional string message = 2;</code>
       *
       * <pre>
       * 消息
       * </pre>
       */
      public Builder setMessageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        message_ = value;
        onChanged();
        return this;
      }

      // optional int64 flowId = 3;
      private long flowId_ ;
      /**
       * <code>optional int64 flowId = 3;</code>
       *
       * <pre>
       * 流水ID
       * </pre>
       */
      public boolean hasFlowId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 flowId = 3;</code>
       *
       * <pre>
       * 流水ID
       * </pre>
       */
      public long getFlowId() {
        return flowId_;
      }
      /**
       * <code>optional int64 flowId = 3;</code>
       *
       * <pre>
       * 流水ID
       * </pre>
       */
      public Builder setFlowId(long value) {
        bitField0_ |= 0x00000004;
        flowId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 flowId = 3;</code>
       *
       * <pre>
       * 流水ID
       * </pre>
       */
      public Builder clearFlowId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        flowId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGrantGameProp)
    }

    static {
      defaultInstance = new ResponseGrantGameProp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGrantGameProp)
  }

  public interface RequestGetGamePropListOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetGamePropList}
   *
   * <pre>
   * GamePropService.java
   * 获取道具列表
   * domain = 4302, op = 261
   * </pre>
   */
  public static final class RequestGetGamePropList extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetGamePropListOrBuilder {
    // Use RequestGetGamePropList.newBuilder() to construct.
    private RequestGetGamePropList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetGamePropList(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetGamePropList defaultInstance;
    public static RequestGetGamePropList getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetGamePropList getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetGamePropList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetGamePropList> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetGamePropList>() {
      public RequestGetGamePropList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetGamePropList(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetGamePropList> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetGamePropList}
     *
     * <pre>
     * GamePropService.java
     * 获取道具列表
     * domain = 4302, op = 261
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropListParam param = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetGamePropList)
    }

    static {
      defaultInstance = new RequestGetGamePropList(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetGamePropList)
  }

  public interface ResponseGetGamePropListOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp> 
        getGamePropsList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp getGameProps(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    int getGamePropsCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder> 
        getGamePropsOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder getGamePropsOrBuilder(
        int index);

    // optional int32 total = 2;
    /**
     * <code>optional int32 total = 2;</code>
     */
    boolean hasTotal();
    /**
     * <code>optional int32 total = 2;</code>
     */
    int getTotal();

    // optional int32 pageNumber = 3;
    /**
     * <code>optional int32 pageNumber = 3;</code>
     */
    boolean hasPageNumber();
    /**
     * <code>optional int32 pageNumber = 3;</code>
     */
    int getPageNumber();

    // optional int32 pageSize = 4;
    /**
     * <code>optional int32 pageSize = 4;</code>
     */
    boolean hasPageSize();
    /**
     * <code>optional int32 pageSize = 4;</code>
     */
    int getPageSize();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetGamePropList}
   *
   * <pre>
   * rcode == 0 (SUCCESS) = 查询成功
   * rcode == 1 (PARAM_ERROR) = 参数错误
   * rcode == 2 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGetGamePropList extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetGamePropListOrBuilder {
    // Use ResponseGetGamePropList.newBuilder() to construct.
    private ResponseGetGamePropList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetGamePropList(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetGamePropList defaultInstance;
    public static ResponseGetGamePropList getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetGamePropList getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetGamePropList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                gameProps_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp>();
                mutable_bitField0_ |= 0x00000001;
              }
              gameProps_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.PARSER, extensionRegistry));
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              total_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              pageNumber_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              pageSize_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          gameProps_ = java.util.Collections.unmodifiableList(gameProps_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetGamePropList> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetGamePropList>() {
      public ResponseGetGamePropList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetGamePropList(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetGamePropList> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;
    public static final int GAMEPROPS_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp> gameProps_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp> getGamePropsList() {
      return gameProps_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder> 
        getGamePropsOrBuilderList() {
      return gameProps_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    public int getGamePropsCount() {
      return gameProps_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp getGameProps(int index) {
      return gameProps_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder getGamePropsOrBuilder(
        int index) {
      return gameProps_.get(index);
    }

    // optional int32 total = 2;
    public static final int TOTAL_FIELD_NUMBER = 2;
    private int total_;
    /**
     * <code>optional int32 total = 2;</code>
     */
    public boolean hasTotal() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 total = 2;</code>
     */
    public int getTotal() {
      return total_;
    }

    // optional int32 pageNumber = 3;
    public static final int PAGENUMBER_FIELD_NUMBER = 3;
    private int pageNumber_;
    /**
     * <code>optional int32 pageNumber = 3;</code>
     */
    public boolean hasPageNumber() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 pageNumber = 3;</code>
     */
    public int getPageNumber() {
      return pageNumber_;
    }

    // optional int32 pageSize = 4;
    public static final int PAGESIZE_FIELD_NUMBER = 4;
    private int pageSize_;
    /**
     * <code>optional int32 pageSize = 4;</code>
     */
    public boolean hasPageSize() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 pageSize = 4;</code>
     */
    public int getPageSize() {
      return pageSize_;
    }

    private void initFields() {
      gameProps_ = java.util.Collections.emptyList();
      total_ = 0;
      pageNumber_ = 0;
      pageSize_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < gameProps_.size(); i++) {
        output.writeMessage(1, gameProps_.get(i));
      }
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(2, total_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(3, pageNumber_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(4, pageSize_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < gameProps_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameProps_.get(i));
      }
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, total_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, pageNumber_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, pageSize_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetGamePropList}
     *
     * <pre>
     * rcode == 0 (SUCCESS) = 查询成功
     * rcode == 1 (PARAM_ERROR) = 参数错误
     * rcode == 2 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGamePropsFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gamePropsBuilder_ == null) {
          gameProps_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          gamePropsBuilder_.clear();
        }
        total_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        pageNumber_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        pageSize_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (gamePropsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            gameProps_ = java.util.Collections.unmodifiableList(gameProps_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.gameProps_ = gameProps_;
        } else {
          result.gameProps_ = gamePropsBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000001;
        }
        result.total_ = total_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000002;
        }
        result.pageNumber_ = pageNumber_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        result.pageSize_ = pageSize_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList.getDefaultInstance()) return this;
        if (gamePropsBuilder_ == null) {
          if (!other.gameProps_.isEmpty()) {
            if (gameProps_.isEmpty()) {
              gameProps_ = other.gameProps_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureGamePropsIsMutable();
              gameProps_.addAll(other.gameProps_);
            }
            onChanged();
          }
        } else {
          if (!other.gameProps_.isEmpty()) {
            if (gamePropsBuilder_.isEmpty()) {
              gamePropsBuilder_.dispose();
              gamePropsBuilder_ = null;
              gameProps_ = other.gameProps_;
              bitField0_ = (bitField0_ & ~0x00000001);
              gamePropsBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getGamePropsFieldBuilder() : null;
            } else {
              gamePropsBuilder_.addAllMessages(other.gameProps_);
            }
          }
        }
        if (other.hasTotal()) {
          setTotal(other.getTotal());
        }
        if (other.hasPageNumber()) {
          setPageNumber(other.getPageNumber());
        }
        if (other.hasPageSize()) {
          setPageSize(other.getPageSize());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp> gameProps_ =
        java.util.Collections.emptyList();
      private void ensureGamePropsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          gameProps_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp>(gameProps_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder> gamePropsBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp> getGamePropsList() {
        if (gamePropsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(gameProps_);
        } else {
          return gamePropsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public int getGamePropsCount() {
        if (gamePropsBuilder_ == null) {
          return gameProps_.size();
        } else {
          return gamePropsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp getGameProps(int index) {
        if (gamePropsBuilder_ == null) {
          return gameProps_.get(index);
        } else {
          return gamePropsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder setGameProps(
          int index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp value) {
        if (gamePropsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePropsIsMutable();
          gameProps_.set(index, value);
          onChanged();
        } else {
          gamePropsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder setGameProps(
          int index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder builderForValue) {
        if (gamePropsBuilder_ == null) {
          ensureGamePropsIsMutable();
          gameProps_.set(index, builderForValue.build());
          onChanged();
        } else {
          gamePropsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder addGameProps(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp value) {
        if (gamePropsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePropsIsMutable();
          gameProps_.add(value);
          onChanged();
        } else {
          gamePropsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder addGameProps(
          int index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp value) {
        if (gamePropsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePropsIsMutable();
          gameProps_.add(index, value);
          onChanged();
        } else {
          gamePropsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder addGameProps(
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder builderForValue) {
        if (gamePropsBuilder_ == null) {
          ensureGamePropsIsMutable();
          gameProps_.add(builderForValue.build());
          onChanged();
        } else {
          gamePropsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder addGameProps(
          int index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder builderForValue) {
        if (gamePropsBuilder_ == null) {
          ensureGamePropsIsMutable();
          gameProps_.add(index, builderForValue.build());
          onChanged();
        } else {
          gamePropsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder addAllGameProps(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp> values) {
        if (gamePropsBuilder_ == null) {
          ensureGamePropsIsMutable();
          super.addAll(values, gameProps_);
          onChanged();
        } else {
          gamePropsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder clearGameProps() {
        if (gamePropsBuilder_ == null) {
          gameProps_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          gamePropsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public Builder removeGameProps(int index) {
        if (gamePropsBuilder_ == null) {
          ensureGamePropsIsMutable();
          gameProps_.remove(index);
          onChanged();
        } else {
          gamePropsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder getGamePropsBuilder(
          int index) {
        return getGamePropsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder getGamePropsOrBuilder(
          int index) {
        if (gamePropsBuilder_ == null) {
          return gameProps_.get(index);  } else {
          return gamePropsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder> 
           getGamePropsOrBuilderList() {
        if (gamePropsBuilder_ != null) {
          return gamePropsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(gameProps_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder addGamePropsBuilder() {
        return getGamePropsFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder addGamePropsBuilder(
          int index) {
        return getGamePropsFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameProp gameProps = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder> 
           getGamePropsBuilderList() {
        return getGamePropsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder> 
          getGamePropsFieldBuilder() {
        if (gamePropsBuilder_ == null) {
          gamePropsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GameProp.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropOrBuilder>(
                  gameProps_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          gameProps_ = null;
        }
        return gamePropsBuilder_;
      }

      // optional int32 total = 2;
      private int total_ ;
      /**
       * <code>optional int32 total = 2;</code>
       */
      public boolean hasTotal() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 total = 2;</code>
       */
      public int getTotal() {
        return total_;
      }
      /**
       * <code>optional int32 total = 2;</code>
       */
      public Builder setTotal(int value) {
        bitField0_ |= 0x00000002;
        total_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 total = 2;</code>
       */
      public Builder clearTotal() {
        bitField0_ = (bitField0_ & ~0x00000002);
        total_ = 0;
        onChanged();
        return this;
      }

      // optional int32 pageNumber = 3;
      private int pageNumber_ ;
      /**
       * <code>optional int32 pageNumber = 3;</code>
       */
      public boolean hasPageNumber() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 pageNumber = 3;</code>
       */
      public int getPageNumber() {
        return pageNumber_;
      }
      /**
       * <code>optional int32 pageNumber = 3;</code>
       */
      public Builder setPageNumber(int value) {
        bitField0_ |= 0x00000004;
        pageNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageNumber = 3;</code>
       */
      public Builder clearPageNumber() {
        bitField0_ = (bitField0_ & ~0x00000004);
        pageNumber_ = 0;
        onChanged();
        return this;
      }

      // optional int32 pageSize = 4;
      private int pageSize_ ;
      /**
       * <code>optional int32 pageSize = 4;</code>
       */
      public boolean hasPageSize() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 pageSize = 4;</code>
       */
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <code>optional int32 pageSize = 4;</code>
       */
      public Builder setPageSize(int value) {
        bitField0_ |= 0x00000008;
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageSize = 4;</code>
       */
      public Builder clearPageSize() {
        bitField0_ = (bitField0_ & ~0x00000008);
        pageSize_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetGamePropList)
    }

    static {
      defaultInstance = new ResponseGetGamePropList(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetGamePropList)
  }

  public interface RequestGetGamePropFlowListOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetGamePropFlowList}
   *
   * <pre>
   * GamePropService.java
   * 获取道具流水列表
   * domain = 4302, op = 262
   * </pre>
   */
  public static final class RequestGetGamePropFlowList extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetGamePropFlowListOrBuilder {
    // Use RequestGetGamePropFlowList.newBuilder() to construct.
    private RequestGetGamePropFlowList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetGamePropFlowList(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetGamePropFlowList defaultInstance;
    public static RequestGetGamePropFlowList getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetGamePropFlowList getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetGamePropFlowList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetGamePropFlowList> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetGamePropFlowList>() {
      public RequestGetGamePropFlowList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetGamePropFlowList(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetGamePropFlowList> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetGamePropFlowList}
     *
     * <pre>
     * GamePropService.java
     * 获取道具流水列表
     * domain = 4302, op = 262
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGamePropFlowListParam param = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetGamePropFlowList)
    }

    static {
      defaultInstance = new RequestGetGamePropFlowList(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetGamePropFlowList)
  }

  public interface ResponseGetGamePropFlowListOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow> 
        getGamePropFlowsList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow getGamePropFlows(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    int getGamePropFlowsCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder> 
        getGamePropFlowsOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder getGamePropFlowsOrBuilder(
        int index);

    // optional int32 total = 2;
    /**
     * <code>optional int32 total = 2;</code>
     */
    boolean hasTotal();
    /**
     * <code>optional int32 total = 2;</code>
     */
    int getTotal();

    // optional int32 pageNumber = 3;
    /**
     * <code>optional int32 pageNumber = 3;</code>
     */
    boolean hasPageNumber();
    /**
     * <code>optional int32 pageNumber = 3;</code>
     */
    int getPageNumber();

    // optional int32 pageSize = 4;
    /**
     * <code>optional int32 pageSize = 4;</code>
     */
    boolean hasPageSize();
    /**
     * <code>optional int32 pageSize = 4;</code>
     */
    int getPageSize();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetGamePropFlowList}
   *
   * <pre>
   * rcode == 0 (SUCCESS) = 查询成功
   * rcode == 1 (PARAM_ERROR) = 参数错误
   * rcode == 2 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGetGamePropFlowList extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetGamePropFlowListOrBuilder {
    // Use ResponseGetGamePropFlowList.newBuilder() to construct.
    private ResponseGetGamePropFlowList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetGamePropFlowList(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetGamePropFlowList defaultInstance;
    public static ResponseGetGamePropFlowList getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetGamePropFlowList getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetGamePropFlowList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                gamePropFlows_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow>();
                mutable_bitField0_ |= 0x00000001;
              }
              gamePropFlows_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.PARSER, extensionRegistry));
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              total_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              pageNumber_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              pageSize_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          gamePropFlows_ = java.util.Collections.unmodifiableList(gamePropFlows_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetGamePropFlowList> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetGamePropFlowList>() {
      public ResponseGetGamePropFlowList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetGamePropFlowList(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetGamePropFlowList> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;
    public static final int GAMEPROPFLOWS_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow> gamePropFlows_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow> getGamePropFlowsList() {
      return gamePropFlows_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder> 
        getGamePropFlowsOrBuilderList() {
      return gamePropFlows_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    public int getGamePropFlowsCount() {
      return gamePropFlows_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow getGamePropFlows(int index) {
      return gamePropFlows_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder getGamePropFlowsOrBuilder(
        int index) {
      return gamePropFlows_.get(index);
    }

    // optional int32 total = 2;
    public static final int TOTAL_FIELD_NUMBER = 2;
    private int total_;
    /**
     * <code>optional int32 total = 2;</code>
     */
    public boolean hasTotal() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 total = 2;</code>
     */
    public int getTotal() {
      return total_;
    }

    // optional int32 pageNumber = 3;
    public static final int PAGENUMBER_FIELD_NUMBER = 3;
    private int pageNumber_;
    /**
     * <code>optional int32 pageNumber = 3;</code>
     */
    public boolean hasPageNumber() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 pageNumber = 3;</code>
     */
    public int getPageNumber() {
      return pageNumber_;
    }

    // optional int32 pageSize = 4;
    public static final int PAGESIZE_FIELD_NUMBER = 4;
    private int pageSize_;
    /**
     * <code>optional int32 pageSize = 4;</code>
     */
    public boolean hasPageSize() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 pageSize = 4;</code>
     */
    public int getPageSize() {
      return pageSize_;
    }

    private void initFields() {
      gamePropFlows_ = java.util.Collections.emptyList();
      total_ = 0;
      pageNumber_ = 0;
      pageSize_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < gamePropFlows_.size(); i++) {
        output.writeMessage(1, gamePropFlows_.get(i));
      }
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(2, total_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(3, pageNumber_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(4, pageSize_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < gamePropFlows_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gamePropFlows_.get(i));
      }
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, total_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, pageNumber_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, pageSize_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetGamePropFlowList}
     *
     * <pre>
     * rcode == 0 (SUCCESS) = 查询成功
     * rcode == 1 (PARAM_ERROR) = 参数错误
     * rcode == 2 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList.class, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGamePropFlowsFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gamePropFlowsBuilder_ == null) {
          gamePropFlows_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          gamePropFlowsBuilder_.clear();
        }
        total_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        pageNumber_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        pageSize_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList build() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList result = new fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (gamePropFlowsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            gamePropFlows_ = java.util.Collections.unmodifiableList(gamePropFlows_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.gamePropFlows_ = gamePropFlows_;
        } else {
          result.gamePropFlows_ = gamePropFlowsBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000001;
        }
        result.total_ = total_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000002;
        }
        result.pageNumber_ = pageNumber_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        result.pageSize_ = pageSize_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList.getDefaultInstance()) return this;
        if (gamePropFlowsBuilder_ == null) {
          if (!other.gamePropFlows_.isEmpty()) {
            if (gamePropFlows_.isEmpty()) {
              gamePropFlows_ = other.gamePropFlows_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureGamePropFlowsIsMutable();
              gamePropFlows_.addAll(other.gamePropFlows_);
            }
            onChanged();
          }
        } else {
          if (!other.gamePropFlows_.isEmpty()) {
            if (gamePropFlowsBuilder_.isEmpty()) {
              gamePropFlowsBuilder_.dispose();
              gamePropFlowsBuilder_ = null;
              gamePropFlows_ = other.gamePropFlows_;
              bitField0_ = (bitField0_ & ~0x00000001);
              gamePropFlowsBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getGamePropFlowsFieldBuilder() : null;
            } else {
              gamePropFlowsBuilder_.addAllMessages(other.gamePropFlows_);
            }
          }
        }
        if (other.hasTotal()) {
          setTotal(other.getTotal());
        }
        if (other.hasPageNumber()) {
          setPageNumber(other.getPageNumber());
        }
        if (other.hasPageSize()) {
          setPageSize(other.getPageSize());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow> gamePropFlows_ =
        java.util.Collections.emptyList();
      private void ensureGamePropFlowsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          gamePropFlows_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow>(gamePropFlows_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder> gamePropFlowsBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow> getGamePropFlowsList() {
        if (gamePropFlowsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(gamePropFlows_);
        } else {
          return gamePropFlowsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public int getGamePropFlowsCount() {
        if (gamePropFlowsBuilder_ == null) {
          return gamePropFlows_.size();
        } else {
          return gamePropFlowsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow getGamePropFlows(int index) {
        if (gamePropFlowsBuilder_ == null) {
          return gamePropFlows_.get(index);
        } else {
          return gamePropFlowsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder setGamePropFlows(
          int index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow value) {
        if (gamePropFlowsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePropFlowsIsMutable();
          gamePropFlows_.set(index, value);
          onChanged();
        } else {
          gamePropFlowsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder setGamePropFlows(
          int index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder builderForValue) {
        if (gamePropFlowsBuilder_ == null) {
          ensureGamePropFlowsIsMutable();
          gamePropFlows_.set(index, builderForValue.build());
          onChanged();
        } else {
          gamePropFlowsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder addGamePropFlows(fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow value) {
        if (gamePropFlowsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePropFlowsIsMutable();
          gamePropFlows_.add(value);
          onChanged();
        } else {
          gamePropFlowsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder addGamePropFlows(
          int index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow value) {
        if (gamePropFlowsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePropFlowsIsMutable();
          gamePropFlows_.add(index, value);
          onChanged();
        } else {
          gamePropFlowsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder addGamePropFlows(
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder builderForValue) {
        if (gamePropFlowsBuilder_ == null) {
          ensureGamePropFlowsIsMutable();
          gamePropFlows_.add(builderForValue.build());
          onChanged();
        } else {
          gamePropFlowsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder addGamePropFlows(
          int index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder builderForValue) {
        if (gamePropFlowsBuilder_ == null) {
          ensureGamePropFlowsIsMutable();
          gamePropFlows_.add(index, builderForValue.build());
          onChanged();
        } else {
          gamePropFlowsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder addAllGamePropFlows(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow> values) {
        if (gamePropFlowsBuilder_ == null) {
          ensureGamePropFlowsIsMutable();
          super.addAll(values, gamePropFlows_);
          onChanged();
        } else {
          gamePropFlowsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder clearGamePropFlows() {
        if (gamePropFlowsBuilder_ == null) {
          gamePropFlows_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          gamePropFlowsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public Builder removeGamePropFlows(int index) {
        if (gamePropFlowsBuilder_ == null) {
          ensureGamePropFlowsIsMutable();
          gamePropFlows_.remove(index);
          onChanged();
        } else {
          gamePropFlowsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder getGamePropFlowsBuilder(
          int index) {
        return getGamePropFlowsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder getGamePropFlowsOrBuilder(
          int index) {
        if (gamePropFlowsBuilder_ == null) {
          return gamePropFlows_.get(index);  } else {
          return gamePropFlowsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder> 
           getGamePropFlowsOrBuilderList() {
        if (gamePropFlowsBuilder_ != null) {
          return gamePropFlowsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(gamePropFlows_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder addGamePropFlowsBuilder() {
        return getGamePropFlowsFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder addGamePropFlowsBuilder(
          int index) {
        return getGamePropFlowsFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePropFlow gamePropFlows = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder> 
           getGamePropFlowsBuilderList() {
        return getGamePropFlowsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder> 
          getGamePropFlowsFieldBuilder() {
        if (gamePropFlowsBuilder_ == null) {
          gamePropFlowsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlow.Builder, fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GamePropFlowOrBuilder>(
                  gamePropFlows_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          gamePropFlows_ = null;
        }
        return gamePropFlowsBuilder_;
      }

      // optional int32 total = 2;
      private int total_ ;
      /**
       * <code>optional int32 total = 2;</code>
       */
      public boolean hasTotal() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 total = 2;</code>
       */
      public int getTotal() {
        return total_;
      }
      /**
       * <code>optional int32 total = 2;</code>
       */
      public Builder setTotal(int value) {
        bitField0_ |= 0x00000002;
        total_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 total = 2;</code>
       */
      public Builder clearTotal() {
        bitField0_ = (bitField0_ & ~0x00000002);
        total_ = 0;
        onChanged();
        return this;
      }

      // optional int32 pageNumber = 3;
      private int pageNumber_ ;
      /**
       * <code>optional int32 pageNumber = 3;</code>
       */
      public boolean hasPageNumber() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 pageNumber = 3;</code>
       */
      public int getPageNumber() {
        return pageNumber_;
      }
      /**
       * <code>optional int32 pageNumber = 3;</code>
       */
      public Builder setPageNumber(int value) {
        bitField0_ |= 0x00000004;
        pageNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageNumber = 3;</code>
       */
      public Builder clearPageNumber() {
        bitField0_ = (bitField0_ & ~0x00000004);
        pageNumber_ = 0;
        onChanged();
        return this;
      }

      // optional int32 pageSize = 4;
      private int pageSize_ ;
      /**
       * <code>optional int32 pageSize = 4;</code>
       */
      public boolean hasPageSize() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 pageSize = 4;</code>
       */
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <code>optional int32 pageSize = 4;</code>
       */
      public Builder setPageSize(int value) {
        bitField0_ |= 0x00000008;
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageSize = 4;</code>
       */
      public Builder clearPageSize() {
        bitField0_ = (bitField0_ & ~0x00000008);
        pageSize_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetGamePropFlowList)
    }

    static {
      defaultInstance = new ResponseGetGamePropFlowList(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetGamePropFlowList)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030protocol_game_prop.proto\022-fm.lizhi.com" +
      "mons.template.datacenter.protocol\"\251\002\n\010Ga" +
      "meProp\022\n\n\002id\030\001 \001(\003\022\025\n\rchannelGameId\030\002 \001(" +
      "\003\022\025\n\rchannelPropId\030\003 \001(\003\022\r\n\005appId\030\004 \001(\003\022" +
      "\021\n\tchannelId\030\005 \001(\003\022\014\n\004name\030\006 \001(\t\022\020\n\010prop" +
      "Desc\030\007 \001(\t\022\014\n\004type\030\010 \001(\005\022\023\n\013durationSec\030" +
      "\t \001(\005\022\022\n\ntimeliness\030\n \001(\010\022\017\n\007iconUrl\030\013 \001" +
      "(\t\022\016\n\006remark\030\014 \001(\t\022\017\n\007deleted\030\r \001(\010\022\020\n\010o" +
      "perator\030\016 \001(\t\022\022\n\ncreateTime\030\017 \001(\003\022\022\n\nmod" +
      "ifyTime\030\020 \001(\003\"\366\001\n\014GamePropFlow\022\n\n\002id\030\001 \001",
      "(\003\022\016\n\006propId\030\002 \001(\003\022\016\n\006userId\030\003 \001(\003\022\020\n\010un" +
      "iqueId\030\004 \001(\t\022\023\n\013durationSec\030\005 \001(\005\022\025\n\rcha" +
      "nnelGameId\030\006 \001(\003\022\025\n\rchannelPropId\030\007 \001(\003\022" +
      "\013\n\003num\030\010 \001(\005\022\r\n\005appId\030\t \001(\003\022\014\n\004type\030\n \001(" +
      "\005\022\023\n\013grantStatus\030\013 \001(\005\022\022\n\ncreateTime\030\014 \001" +
      "(\003\022\022\n\nmodifyTime\030\r \001(\003\"\225\001\n\022GrantGameProp" +
      "Param\022\016\n\006userId\030\001 \001(\003\022\016\n\006propId\030\002 \001(\003\022\013\n" +
      "\003num\030\003 \001(\005\022\020\n\010uniqueId\030\004 \001(\t\022\r\n\005appId\030\005 " +
      "\001(\t\022\016\n\006gameId\030\006 \001(\t\022\017\n\007channel\030\007 \001(\t\022\020\n\010" +
      "duration\030\010 \001(\003\"t\n\024GetGamePropListParam\022\025",
      "\n\rchannelGameId\030\001 \001(\003\022\021\n\tchannelId\030\002 \001(\003" +
      "\022\014\n\004type\030\003 \001(\005\022\022\n\npageNumber\030\004 \001(\005\022\020\n\010pa" +
      "geSize\030\005 \001(\005\"\231\001\n\030GetGamePropFlowListPara" +
      "m\022\016\n\006userId\030\001 \001(\003\022\016\n\006propId\030\002 \001(\003\022\023\n\013gra" +
      "ntStatus\030\003 \001(\005\022\021\n\tstartTime\030\004 \001(\003\022\017\n\007end" +
      "Time\030\005 \001(\003\022\022\n\npageNumber\030\006 \001(\005\022\020\n\010pageSi" +
      "ze\030\007 \001(\005\"h\n\024RequestGrantGameProp\022P\n\005para" +
      "m\030\001 \001(\0132A.fm.lizhi.commons.template.data" +
      "center.protocol.GrantGamePropParam\"M\n\025Re" +
      "sponseGrantGameProp\022\023\n\013grantStatus\030\001 \001(\005",
      "\022\017\n\007message\030\002 \001(\t\022\016\n\006flowId\030\003 \001(\003\"l\n\026Req" +
      "uestGetGamePropList\022R\n\005param\030\001 \001(\0132C.fm." +
      "lizhi.commons.template.datacenter.protoc" +
      "ol.GetGamePropListParam\"\232\001\n\027ResponseGetG" +
      "amePropList\022J\n\tgameProps\030\001 \003(\01327.fm.lizh" +
      "i.commons.template.datacenter.protocol.G" +
      "ameProp\022\r\n\005total\030\002 \001(\005\022\022\n\npageNumber\030\003 \001" +
      "(\005\022\020\n\010pageSize\030\004 \001(\005\"t\n\032RequestGetGamePr" +
      "opFlowList\022V\n\005param\030\001 \001(\0132G.fm.lizhi.com" +
      "mons.template.datacenter.protocol.GetGam",
      "ePropFlowListParam\"\246\001\n\033ResponseGetGamePr" +
      "opFlowList\022R\n\rgamePropFlows\030\001 \003(\0132;.fm.l" +
      "izhi.commons.template.datacenter.protoco" +
      "l.GamePropFlow\022\r\n\005total\030\002 \001(\005\022\022\n\npageNum" +
      "ber\030\003 \001(\005\022\020\n\010pageSize\030\004 \001(\005B4\n\034fm.lizhi." +
      "ocean.seal.protocolB\024GamePropServiceProt" +
      "o"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameProp_descriptor,
              new java.lang.String[] { "Id", "ChannelGameId", "ChannelPropId", "AppId", "ChannelId", "Name", "PropDesc", "Type", "DurationSec", "Timeliness", "IconUrl", "Remark", "Deleted", "Operator", "CreateTime", "ModifyTime", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePropFlow_descriptor,
              new java.lang.String[] { "Id", "PropId", "UserId", "UniqueId", "DurationSec", "ChannelGameId", "ChannelPropId", "Num", "AppId", "Type", "GrantStatus", "CreateTime", "ModifyTime", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GrantGamePropParam_descriptor,
              new java.lang.String[] { "UserId", "PropId", "Num", "UniqueId", "AppId", "GameId", "Channel", "Duration", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropListParam_descriptor,
              new java.lang.String[] { "ChannelGameId", "ChannelId", "Type", "PageNumber", "PageSize", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGamePropFlowListParam_descriptor,
              new java.lang.String[] { "UserId", "PropId", "GrantStatus", "StartTime", "EndTime", "PageNumber", "PageSize", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGrantGameProp_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGrantGameProp_descriptor,
              new java.lang.String[] { "GrantStatus", "Message", "FlowId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropList_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_descriptor =
            getDescriptor().getMessageTypes().get(8);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropList_descriptor,
              new java.lang.String[] { "GameProps", "Total", "PageNumber", "PageSize", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_descriptor =
            getDescriptor().getMessageTypes().get(9);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGamePropFlowList_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_descriptor =
            getDescriptor().getMessageTypes().get(10);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGamePropFlowList_descriptor,
              new java.lang.String[] { "GamePropFlows", "Total", "PageNumber", "PageSize", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
